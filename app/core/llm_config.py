from openai import OpenAI
from groq import Groq

from app.core.config import settings

# For open ai
client = OpenAI(api_key=settings.OPENAI_API_KEY)

# For Groq (Llama model)
groq_client = Groq(api_key=settings.GROQ_API_KEY)

def call_groq_with_fallback(messages, temperature=0.2, max_tokens=4096, stream=False):
    """
    Call Groq Llama model with fallback to GPT-4o

    Args:
        messages: List of message dictionaries
        temperature: Temperature for generation
        max_tokens: Maximum tokens to generate
        stream: Whether to stream the response

    Returns:
        Response object or generator (if streaming)
    """
    try:
        # Try Groq first
        response = groq_client.chat.completions.create(
            model="meta-llama/llama-4-scout-17b-16e-instruct",
            messages=messages,
            temperature=temperature,
            max_completion_tokens=max_tokens,
            stream=stream,
            top_p=1,
            stop=None,
        )
        return response, "groq"

    except Exception as e:
        # Fallback to GPT-4o
        print(f"Groq API failed, falling back to GPT-4o: {str(e)}")
        response = client.chat.completions.create(
            model=settings.MODEL_NAME,  # This is gpt-4o
            messages=messages,
            temperature=temperature,
            max_tokens=max_tokens,
            stream=stream,
        )
        return response, "openai"

# # Create an OpenAI client with your deepinfra token and endpoint
# client = OpenAI(
#     api_key= os.getenv("DEEP_INRA_API"),
#     base_url= os.getenv("DEEP_INRA_BASE_URL"),
# )
