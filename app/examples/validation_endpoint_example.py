"""
Example of how to implement endpoints with comprehensive validation error handling

This file demonstrates best practices for implementing API endpoints
with robust input validation using our standardized error handling framework.
"""

from typing import List, Optional
from datetime import datetime

from fastapi import APIRouter, Depends, File, HTTPException, Query, Request, UploadFile
from pydantic import BaseModel, EmailStr, Field, ValidationError, validator
from sqlalchemy.ext.asyncio import AsyncSession

from app.db.database import get_db
from app.utils.error_handler import ErrorHandler
from app.utils.validation_error_handler import (
    ValidationErrorHandler,
    validate_file_upload,
    validate_positive_int,
    validate_query_parameters,
    validate_request_data,
    validate_uuid,
)

router = APIRouter()


class UserCreateRequest(BaseModel):
    """User creation request with comprehensive validation"""
    
    name: str = Field(..., min_length=2, max_length=100, description="User's full name")
    email: EmailStr = Field(..., description="User's email address")
    age: int = Field(..., ge=18, le=120, description="User's age (18-120)")
    phone: Optional[str] = Field(None, regex=r'^\+?1?\d{9,15}$', description="Phone number")
    tags: List[str] = Field(default=[], max_items=10, description="User tags")
    
    @validator('name')
    def validate_name(cls, v):
        if not v.strip():
            raise ValueError('Name cannot be empty or whitespace only')
        if any(char.isdigit() for char in v):
            raise ValueError('Name cannot contain numbers')
        return v.strip()
    
    @validator('tags')
    def validate_tags(cls, v):
        if v:
            for tag in v:
                if len(tag.strip()) < 2:
                    raise ValueError('Each tag must be at least 2 characters long')
        return [tag.strip() for tag in v if tag.strip()]


class UserUpdateRequest(BaseModel):
    """User update request with optional fields"""
    
    name: Optional[str] = Field(None, min_length=2, max_length=100)
    email: Optional[EmailStr] = None
    age: Optional[int] = Field(None, ge=18, le=120)
    phone: Optional[str] = Field(None, regex=r'^\+?1?\d{9,15}$')
    tags: Optional[List[str]] = Field(None, max_items=10)


class SearchRequest(BaseModel):
    """Search request with validation"""
    
    query: str = Field(..., min_length=1, max_length=500, description="Search query")
    filters: Optional[dict] = Field(default={}, description="Search filters")
    sort_by: Optional[str] = Field("created_at", description="Sort field")
    sort_order: Optional[str] = Field("desc", regex=r'^(asc|desc)$', description="Sort order")
    
    @validator('query')
    def validate_query(cls, v):
        # Remove excessive whitespace
        cleaned = ' '.join(v.split())
        if not cleaned:
            raise ValueError('Query cannot be empty after cleaning')
        return cleaned


@router.post("/users")
@validate_request_data(UserCreateRequest)
async def create_user(
    request: Request,
    user_data: UserCreateRequest,
    db: AsyncSession = Depends(get_db)
):
    """
    Create a new user with comprehensive validation
    
    This endpoint demonstrates:
    1. Request body validation using Pydantic
    2. Custom validators
    3. Standardized error responses
    """
    try:
        # Simulate user creation logic
        user_dict = user_data.dict()
        user_dict['id'] = "user_123"
        user_dict['created_at'] = datetime.utcnow().isoformat()
        
        return {
            "message": "User created successfully",
            "user": user_dict
        }
    
    except Exception as e:
        error = ValidationErrorHandler.create_validation_error(
            message=f"Failed to create user: {str(e)}"
        )
        return ErrorHandler.create_error_response(error, request)


@router.put("/users/{user_id}")
@validate_request_data(UserUpdateRequest)
async def update_user(
    request: Request,
    user_id: str,
    user_data: UserUpdateRequest,
    db: AsyncSession = Depends(get_db)
):
    """
    Update user with validation
    
    Demonstrates path parameter validation and partial updates
    """
    try:
        # Validate user_id format
        validate_uuid(user_id)
        
        # Simulate user update logic
        updated_fields = {k: v for k, v in user_data.dict().items() if v is not None}
        
        return {
            "message": "User updated successfully",
            "user_id": user_id,
            "updated_fields": updated_fields
        }
    
    except ValueError as e:
        error = ValidationErrorHandler.create_validation_error(
            message=str(e),
            field="user_id"
        )
        return ErrorHandler.create_error_response(error, request)
    except Exception as e:
        error = ValidationErrorHandler.create_validation_error(
            message=f"Failed to update user: {str(e)}"
        )
        return ErrorHandler.create_error_response(error, request)


@router.get("/users")
@validate_query_parameters(
    page=validate_positive_int,
    limit=validate_positive_int,
    sort_by=lambda x: x if x in ['name', 'email', 'created_at'] else None
)
async def get_users(
    request: Request,
    page: int = Query(1, description="Page number"),
    limit: int = Query(10, description="Items per page"),
    sort_by: str = Query("created_at", description="Sort field"),
    search: Optional[str] = Query(None, description="Search query"),
    db: AsyncSession = Depends(get_db)
):
    """
    Get users with query parameter validation
    
    Demonstrates:
    1. Query parameter validation
    2. Custom validators for specific fields
    3. Optional parameters
    """
    try:
        # Additional validation for search parameter
        if search and len(search.strip()) < 2:
            error = ValidationErrorHandler.create_validation_error(
                message="Search query must be at least 2 characters long",
                field="search"
            )
            return ErrorHandler.create_error_response(error, request)
        
        # Simulate user retrieval logic
        users = [
            {
                "id": f"user_{i}",
                "name": f"User {i}",
                "email": f"user{i}@example.com",
                "created_at": datetime.utcnow().isoformat()
            }
            for i in range(1, limit + 1)
        ]
        
        return {
            "users": users,
            "pagination": {
                "page": page,
                "limit": limit,
                "total": 100,
                "pages": 10
            },
            "filters": {
                "sort_by": sort_by,
                "search": search
            }
        }
    
    except Exception as e:
        error = ValidationErrorHandler.create_validation_error(
            message=f"Failed to retrieve users: {str(e)}"
        )
        return ErrorHandler.create_error_response(error, request)


@router.post("/users/{user_id}/avatar")
@validate_file_upload(
    allowed_types=["image/jpeg", "image/png", "image/gif"],
    max_size_mb=5,
    required=True
)
async def upload_user_avatar(
    request: Request,
    user_id: str,
    file: UploadFile = File(...),
    db: AsyncSession = Depends(get_db)
):
    """
    Upload user avatar with file validation
    
    Demonstrates:
    1. File upload validation
    2. File type and size restrictions
    3. Path parameter validation
    """
    try:
        # Validate user_id
        validate_uuid(user_id)
        
        # Simulate file processing
        file_info = {
            "filename": file.filename,
            "content_type": file.content_type,
            "size": file.size if hasattr(file, 'size') else len(await file.read())
        }
        
        # Reset file pointer after reading
        await file.seek(0)
        
        return {
            "message": "Avatar uploaded successfully",
            "user_id": user_id,
            "file_info": file_info
        }
    
    except ValueError as e:
        error = ValidationErrorHandler.create_validation_error(
            message=str(e),
            field="user_id"
        )
        return ErrorHandler.create_error_response(error, request)
    except Exception as e:
        error = ValidationErrorHandler.create_validation_error(
            message=f"Failed to upload avatar: {str(e)}"
        )
        return ErrorHandler.create_error_response(error, request)


@router.post("/search")
@validate_request_data(SearchRequest)
async def search_content(
    request: Request,
    search_data: SearchRequest,
    db: AsyncSession = Depends(get_db)
):
    """
    Search content with complex validation
    
    Demonstrates:
    1. Complex request body validation
    2. Custom field validators
    3. Nested object validation
    """
    try:
        # Additional business logic validation
        if search_data.filters:
            allowed_filters = ['category', 'status', 'date_range']
            invalid_filters = set(search_data.filters.keys()) - set(allowed_filters)
            
            if invalid_filters:
                error = ValidationErrorHandler.create_validation_error(
                    message=f"Invalid filter fields: {', '.join(invalid_filters)}",
                    field="filters",
                    additional_info={
                        "invalid_filters": list(invalid_filters),
                        "allowed_filters": allowed_filters
                    }
                )
                return ErrorHandler.create_error_response(error, request)
        
        # Simulate search logic
        results = [
            {
                "id": f"result_{i}",
                "title": f"Search Result {i}",
                "content": f"Content matching '{search_data.query}'",
                "score": 0.9 - (i * 0.1)
            }
            for i in range(1, 6)
        ]
        
        return {
            "query": search_data.query,
            "results": results,
            "total": len(results),
            "filters_applied": search_data.filters,
            "sort": {
                "field": search_data.sort_by,
                "order": search_data.sort_order
            }
        }
    
    except Exception as e:
        error = ValidationErrorHandler.create_validation_error(
            message=f"Search failed: {str(e)}"
        )
        return ErrorHandler.create_error_response(error, request)


# Example of handling validation errors in middleware
async def validation_error_middleware(request: Request, call_next):
    """
    Example middleware to handle validation errors globally
    """
    try:
        response = await call_next(request)
        return response
    except ValidationError as e:
        # Handle Pydantic validation errors
        formatted_errors = ValidationErrorHandler.format_pydantic_errors(e.errors())
        validation_error = ValidationErrorHandler.create_validation_error(
            message="Request validation failed",
            errors=formatted_errors
        )
        return ErrorHandler.create_error_response(validation_error, request)
    except Exception as e:
        # Handle other errors
        error = ValidationErrorHandler.create_validation_error(
            message=f"Unexpected error: {str(e)}"
        )
        return ErrorHandler.create_error_response(error, request)
