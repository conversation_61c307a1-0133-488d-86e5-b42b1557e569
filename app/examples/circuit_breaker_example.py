"""
Circuit Breaker Pattern Examples

This module demonstrates how to use and monitor circuit breakers
for external service calls to prevent cascading failures.
"""

import asyncio
import time
from typing import Dict, Any

from fastapi import APIRouter, HTTPException, Request
from pydantic import BaseModel

from app.utils.api_error_handler import CircuitBreaker, with_circuit_breaker
from app.utils.error_handler import ExternalAPIError, ErrorSeverity
from app.utils.logger import chatbot_logger as logger

router = APIRouter()


class ServiceRequest(BaseModel):
    service_name: str
    data: Dict[str, Any] = {}


class CircuitBreakerStatus(BaseModel):
    service_name: str
    state: str
    failure_count: int
    last_failure_time: float = None
    half_open_calls: int = 0


# Example service functions with circuit breakers
@with_circuit_breaker("example_service_1")
async def call_example_service_1(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Example external service call with circuit breaker protection
    """
    # Simulate service call
    await asyncio.sleep(0.1)
    
    # Simulate random failures for demonstration
    import random
    if random.random() < 0.3:  # 30% failure rate
        raise ExternalAPIError(
            service="example_service_1",
            message="Service temporarily unavailable",
            status_code=503,
            severity=ErrorSeverity.HIGH
        )
    
    return {"result": "success", "data": data, "service": "example_service_1"}


@with_circuit_breaker("example_service_2")
async def call_example_service_2(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Another example service with different failure characteristics
    """
    await asyncio.sleep(0.2)
    
    # Simulate timeout errors
    import random
    if random.random() < 0.2:  # 20% failure rate
        raise ExternalAPIError(
            service="example_service_2",
            message="Service request timed out",
            status_code=408,
            severity=ErrorSeverity.MEDIUM
        )
    
    return {"result": "success", "data": data, "service": "example_service_2"}


@with_circuit_breaker("unreliable_service")
async def call_unreliable_service(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Example of a highly unreliable service for testing circuit breaker
    """
    await asyncio.sleep(0.05)
    
    # High failure rate to trigger circuit breaker
    import random
    if random.random() < 0.8:  # 80% failure rate
        raise ExternalAPIError(
            service="unreliable_service",
            message="Service is highly unreliable",
            status_code=500,
            severity=ErrorSeverity.HIGH
        )
    
    return {"result": "success", "data": data, "service": "unreliable_service"}


@router.post("/circuit-breaker/test/{service_name}")
async def test_circuit_breaker(service_name: str, request_data: ServiceRequest):
    """
    Test circuit breaker functionality with different services
    """
    try:
        service_functions = {
            "service_1": call_example_service_1,
            "service_2": call_example_service_2,
            "unreliable": call_unreliable_service
        }
        
        if service_name not in service_functions:
            raise HTTPException(
                status_code=400,
                detail=f"Unknown service: {service_name}"
            )
        
        service_func = service_functions[service_name]
        result = await service_func(request_data.data)
        
        return {
            "success": True,
            "result": result,
            "circuit_breaker_status": get_circuit_breaker_status(service_name)
        }
    
    except ExternalAPIError as e:
        return {
            "success": False,
            "error": {
                "code": e.code.value,
                "message": e.message,
                "service": service_name
            },
            "circuit_breaker_status": get_circuit_breaker_status(service_name)
        }
    except Exception as e:
        logger.error(f"Unexpected error in circuit breaker test: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/circuit-breaker/status")
async def get_all_circuit_breaker_status():
    """
    Get status of all circuit breakers
    """
    try:
        # Get all circuit breaker instances
        circuit_breakers = CircuitBreaker._instances
        
        status_list = []
        for service_name, breaker in circuit_breakers.items():
            status_list.append(CircuitBreakerStatus(
                service_name=service_name,
                state=breaker.state.value,
                failure_count=breaker.failure_count,
                last_failure_time=breaker.last_failure_time,
                half_open_calls=breaker.half_open_calls
            ))
        
        return {
            "circuit_breakers": status_list,
            "total_services": len(status_list),
            "timestamp": time.time()
        }
    
    except Exception as e:
        logger.error(f"Failed to get circuit breaker status: {e}")
        raise HTTPException(status_code=500, detail="Failed to get status")


@router.get("/circuit-breaker/status/{service_name}")
async def get_circuit_breaker_status_by_service(service_name: str):
    """
    Get detailed status of a specific circuit breaker
    """
    try:
        status = get_circuit_breaker_status(service_name)
        if not status:
            raise HTTPException(
                status_code=404,
                detail=f"Circuit breaker not found for service: {service_name}"
            )
        
        return status
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get circuit breaker status for {service_name}: {e}")
        raise HTTPException(status_code=500, detail="Failed to get status")


@router.post("/circuit-breaker/reset/{service_name}")
async def reset_circuit_breaker(service_name: str):
    """
    Reset a circuit breaker (for testing/admin purposes)
    """
    try:
        circuit_breakers = CircuitBreaker._instances
        
        if service_name not in circuit_breakers:
            raise HTTPException(
                status_code=404,
                detail=f"Circuit breaker not found for service: {service_name}"
            )
        
        breaker = circuit_breakers[service_name]
        
        # Reset circuit breaker state
        from app.utils.api_error_handler import CircuitBreakerState
        breaker.state = CircuitBreakerState.CLOSED
        breaker.failure_count = 0
        breaker.last_failure_time = None
        breaker.half_open_calls = 0
        
        logger.info(f"Circuit breaker reset for service: {service_name}")
        
        return {
            "message": f"Circuit breaker reset for service: {service_name}",
            "status": get_circuit_breaker_status(service_name)
        }
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to reset circuit breaker for {service_name}: {e}")
        raise HTTPException(status_code=500, detail="Failed to reset circuit breaker")


@router.post("/circuit-breaker/load-test/{service_name}")
async def load_test_circuit_breaker(
    service_name: str,
    concurrent_requests: int = 10,
    total_requests: int = 50
):
    """
    Load test a service to demonstrate circuit breaker behavior
    """
    try:
        service_functions = {
            "service_1": call_example_service_1,
            "service_2": call_example_service_2,
            "unreliable": call_unreliable_service
        }
        
        if service_name not in service_functions:
            raise HTTPException(
                status_code=400,
                detail=f"Unknown service: {service_name}"
            )
        
        service_func = service_functions[service_name]
        
        # Track results
        results = {
            "successful": 0,
            "failed": 0,
            "circuit_open": 0,
            "errors": []
        }
        
        # Run load test
        semaphore = asyncio.Semaphore(concurrent_requests)
        
        async def make_request(request_id: int):
            async with semaphore:
                try:
                    await service_func({"request_id": request_id})
                    results["successful"] += 1
                except ExternalAPIError as e:
                    if "circuit open" in e.message.lower():
                        results["circuit_open"] += 1
                    else:
                        results["failed"] += 1
                    results["errors"].append({
                        "request_id": request_id,
                        "error": e.message,
                        "code": e.code.value
                    })
                except Exception as e:
                    results["failed"] += 1
                    results["errors"].append({
                        "request_id": request_id,
                        "error": str(e),
                        "code": "UNKNOWN"
                    })
        
        # Execute requests
        tasks = [make_request(i) for i in range(total_requests)]
        await asyncio.gather(*tasks, return_exceptions=True)
        
        return {
            "load_test_results": results,
            "test_parameters": {
                "service_name": service_name,
                "concurrent_requests": concurrent_requests,
                "total_requests": total_requests
            },
            "final_circuit_breaker_status": get_circuit_breaker_status(service_name)
        }
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Load test failed for {service_name}: {e}")
        raise HTTPException(status_code=500, detail="Load test failed")


def get_circuit_breaker_status(service_name: str) -> Dict[str, Any]:
    """
    Helper function to get circuit breaker status
    """
    circuit_breakers = CircuitBreaker._instances
    
    if service_name not in circuit_breakers:
        return None
    
    breaker = circuit_breakers[service_name]
    
    return {
        "service_name": service_name,
        "state": breaker.state.value,
        "failure_count": breaker.failure_count,
        "failure_threshold": breaker.failure_threshold,
        "recovery_timeout": breaker.recovery_timeout,
        "last_failure_time": breaker.last_failure_time,
        "half_open_calls": breaker.half_open_calls,
        "half_open_max_calls": breaker.half_open_max_calls,
        "can_make_request": breaker.allow_request()
    }


# Example of how to configure circuit breakers for different services
def configure_circuit_breakers():
    """
    Example configuration for different services with custom parameters
    """
    # High-reliability service - more tolerant
    reliable_service_breaker = CircuitBreaker(
        service_name="reliable_service",
        failure_threshold=10,  # Allow more failures
        recovery_timeout=60,   # Longer recovery time
        half_open_max_calls=5
    )
    
    # Critical service - less tolerant
    critical_service_breaker = CircuitBreaker(
        service_name="critical_service",
        failure_threshold=3,   # Fail fast
        recovery_timeout=30,   # Quick recovery attempts
        half_open_max_calls=2
    )
    
    # External API - moderate tolerance
    external_api_breaker = CircuitBreaker(
        service_name="external_api",
        failure_threshold=5,
        recovery_timeout=45,
        half_open_max_calls=3
    )
    
    return {
        "reliable_service": reliable_service_breaker,
        "critical_service": critical_service_breaker,
        "external_api": external_api_breaker
    }
