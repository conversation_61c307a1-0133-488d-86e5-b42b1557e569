"""
Example of how to implement streaming endpoints with comprehensive error handling

This file demonstrates best practices for implementing streaming endpoints
using our standardized error handling framework.
"""

import asyncio
import json
from typing import AsyncGenerator

from fastapi import APIRouter, Depends, HTTPException, Request
from fastapi.responses import StreamingResponse
from pydantic import BaseModel
from sqlalchemy.ext.asyncio import AsyncSession

from app.db.database import get_db
from app.utils.error_handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, StandardizedError
from app.utils.streaming_error_handler import (
    StreamingError,
    handle_streaming_errors,
    safe_streaming_generator,
    stream_with_timeout,
)
from app.utils.validation_error_handler import validate_request_data

router = APIRouter()


class StreamingRequest(BaseModel):
    query: str
    max_results: int = 10
    timeout_seconds: float = 30.0


class StreamingResponse(BaseModel):
    message: str
    progress: int
    total: int
    data: dict = {}


@router.post("/streaming-example")
@handle_streaming_errors(
    error_message="An error occurred during streaming processing",
    include_html_format=True
)
async def streaming_example_endpoint(
    request: Request,
    data: StreamingRequest,
    db: AsyncSession = Depends(get_db)
):
    """
    Example streaming endpoint with comprehensive error handling
    
    This endpoint demonstrates:
    1. Request validation
    2. Streaming response generation
    3. Timeout handling
    4. Error recovery
    5. User-friendly error messages
    """
    
    async def process_streaming_data() -> AsyncGenerator[str, None]:
        """
        Main streaming processing function
        """
        try:
            # Simulate processing steps
            total_steps = data.max_results
            
            for i in range(total_steps):
                # Simulate some processing work
                await asyncio.sleep(0.1)
                
                # Check for potential errors (simulate random failures)
                if i == 5 and data.query == "error_test":
                    raise StreamingError(
                        message="Simulated processing error",
                        operation="data_processing",
                        additional_info={"step": i, "query": data.query}
                    )
                
                # Create response chunk
                response_chunk = StreamingResponse(
                    message=f"Processing step {i + 1}",
                    progress=i + 1,
                    total=total_steps,
                    data={"step_result": f"Result for step {i + 1}"}
                )
                
                # Yield the chunk as JSON
                yield f"data: {response_chunk.json()}\n\n"
            
            # Final completion message
            final_response = {
                "message": "Processing completed successfully",
                "progress": total_steps,
                "total": total_steps,
                "type": "complete"
            }
            yield f"data: {json.dumps(final_response)}\n\n"
            
        except StreamingError:
            # Re-raise streaming errors to be handled by the framework
            raise
        except Exception as e:
            # Convert unexpected errors to streaming errors
            raise StreamingError(
                message=f"Unexpected error during processing: {str(e)}",
                operation="streaming_processing",
                original_exception=e
            )
    
    # Use timeout wrapper for the streaming generator
    timeout_generator = stream_with_timeout(
        process_streaming_data,
        timeout_seconds=data.timeout_seconds,
        timeout_message="Processing timed out. Please try again with a smaller dataset."
    )
    
    return StreamingResponse(
        timeout_generator,
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "X-Accel-Buffering": "no"  # Disable nginx buffering
        }
    )


@router.post("/streaming-with-recovery")
async def streaming_with_recovery_endpoint(
    request: Request,
    data: StreamingRequest,
    db: AsyncSession = Depends(get_db)
):
    """
    Example streaming endpoint with partial results recovery
    
    This endpoint demonstrates how to handle errors gracefully
    while preserving partial results.
    """
    
    async def process_with_recovery() -> AsyncGenerator[str, None]:
        """
        Processing function with partial results recovery
        """
        results = []
        
        try:
            for i in range(data.max_results):
                await asyncio.sleep(0.1)
                
                # Simulate processing
                result = {"id": i, "data": f"Processed item {i}"}
                results.append(result)
                
                # Yield intermediate result
                chunk = {
                    "type": "progress",
                    "message": f"Processed item {i + 1}/{data.max_results}",
                    "result": result,
                    "progress": i + 1,
                    "total": data.max_results
                }
                yield f"data: {json.dumps(chunk)}\n\n"
                
                # Simulate error at step 7
                if i == 7 and data.query == "partial_error":
                    raise Exception("Simulated processing error")
            
            # Success completion
            final_chunk = {
                "type": "complete",
                "message": "All items processed successfully",
                "results": results,
                "total_processed": len(results)
            }
            yield f"data: {json.dumps(final_chunk)}\n\n"
            
        except Exception as e:
            # Handle error with partial results
            error_chunk = {
                "type": "partial_error",
                "message": f"Processing failed after {len(results)} items: {str(e)}",
                "partial_results": results,
                "total_processed": len(results),
                "error": str(e)
            }
            yield f"data: {json.dumps(error_chunk)}\n\n"
    
    return StreamingResponse(
        safe_streaming_generator(
            process_with_recovery,
            lambda e: create_recovery_error_chunk(e, data.query)
        ),
        media_type="text/event-stream"
    )


def create_recovery_error_chunk(exception: Exception, query: str) -> str:
    """
    Create a custom error chunk for recovery scenarios
    """
    if isinstance(exception, StandardizedError):
        error = exception
    else:
        error = StreamingError(
            message="Processing failed with partial results",
            operation="streaming_recovery",
            original_exception=exception
        )
    
    error_data = {
        "type": "recovery_error",
        "error": {
            "code": error.code.value,
            "message": error.message,
            "category": error.category.value
        },
        "recovery_info": {
            "query": query,
            "suggestion": "You can retry the request or contact support if the issue persists."
        }
    }
    
    return f"data: {json.dumps(error_data)}\n\n"


@router.get("/streaming-health")
async def streaming_health_check():
    """
    Health check endpoint for streaming services
    """
    try:
        # Test basic streaming functionality
        async def health_stream():
            for i in range(3):
                await asyncio.sleep(0.1)
                yield f"data: {json.dumps({'status': 'ok', 'step': i + 1})}\n\n"
        
        return StreamingResponse(
            health_stream(),
            media_type="text/event-stream"
        )
    
    except Exception as e:
        error = StreamingError(
            message="Streaming health check failed",
            operation="health_check",
            original_exception=e
        )
        return ErrorHandler.create_error_response(error)


# Example of how to use the streaming error handler in existing endpoints
async def upgrade_existing_streaming_endpoint():
    """
    Example of how to upgrade an existing streaming endpoint
    to use the new error handling framework
    """
    
    # Before: Basic streaming with minimal error handling
    async def old_streaming_function():
        try:
            for i in range(10):
                yield f"data: {json.dumps({'step': i})}\n\n"
        except Exception as e:
            yield f"data: {json.dumps({'error': str(e)})}\n\n"
    
    # After: Enhanced streaming with comprehensive error handling
    async def new_streaming_function():
        try:
            for i in range(10):
                # Simulate potential error
                if i == 5:
                    raise StreamingError(
                        message="Processing error at step 5",
                        operation="data_processing",
                        additional_info={"step": i}
                    )
                
                yield f"data: {json.dumps({'step': i})}\n\n"
                
        except StreamingError:
            # Re-raise to be handled by framework
            raise
        except Exception as e:
            # Convert to standardized error
            raise StreamingError(
                message=f"Unexpected error: {str(e)}",
                operation="streaming_processing",
                original_exception=e
            )
    
    # Use the safe streaming generator wrapper
    return StreamingResponse(
        safe_streaming_generator(new_streaming_function),
        media_type="text/event-stream"
    )
