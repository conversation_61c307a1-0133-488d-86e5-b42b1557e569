from typing import Literal, Optional

from fastapi import APIRouter
from fastapi.exceptions import HTTPException
from fastapi.param_functions import Depends
from fastapi.requests import Request
from fastapi.responses import JSONResponse
from fastapi_pagination import Params, add_pagination
from sqlalchemy.ext.asyncio import AsyncSession

from app.crud.chat_crud import (
    get_chat_node,
    get_chats_by_chat_id,
    get_conversation_by_chat_id,
    get_conversations,
    give_feedback,
    remove_conversation_by_chat_id,
    remove_conversations_by_chat_ids,
    rename_conversation_by_chat_id,
    update_chat,
)
from app.db.database import get_db
from app.schemas.chat_schemas import ChatUpdateSchema
from app.utils import cache
from app.utils.logger import generic_logger as logger

router = APIRouter()

add_pagination(router)


@router.get("/conversations")
async def get_chat_record_route(
    request: Request,
    chat_type: Optional[Literal["", "normal", "agent", "media", "drawing"]] = "normal",
    account_id: Optional[str] = "",
    agent_id: Optional[int] = None,
    knowledge_id: Optional[int] = None,
    params: Params = Depends(),
    db: AsyncSession = Depends(get_db),
):
    if chat_type == "agent" and not agent_id:
        logger.warning("Agent ID is required for agent chat type")
        raise HTTPException(
            status_code=400, detail="Agent ID is required for agent chat type"
        )
    if chat_type == "media" and not knowledge_id:
        logger.warning("Knowledge ID is required for media chat type")
        raise HTTPException(
            status_code=400, detail="Knowledge ID is required for media chat type"
        )
    if account_id:
        customer_id = request.state.customer_id
        account_id = f"{customer_id}_{account_id}"
    else:
        account_id = f"{request.state.customer_id}_0"
    logger.info(
        f"Fetching conversations for Account ID: {account_id}, Chat Type: {chat_type}"
    )
    conversations = await get_conversations(
        db,
        account_id,
        chat_type.upper(),
        agent_id=agent_id,
        knowledge_id=knowledge_id,
        params=params,
    )
    if not conversations:
        logger.warning(
            f"No conversations found for Account ID: {account_id}, Chat Type: {chat_type}"
        )
        raise HTTPException(status_code=404, detail="No conversations found")
    return conversations


@router.get("/set-chat-active")
async def set_chat_active_new_route(request: Request, chat_id: str):
    chat = await get_chat_node(id=chat_id)
    if not chat:
        logger.warning(f"Chat record not found: {chat_id}")
        raise HTTPException(status_code=404, detail="Chat not found")
    parent_chat = await get_chat_node(id=chat.get("parent"))
    children = parent_chat.get("children", [])
    active = children.index(chat_id)
    await update_chat(id=parent_chat.get("id"), children=children, active=active)
    return {"message": "Chat updated successfully."}


@router.get("/get-chat")
async def get_chat_new_route(
    request: Request,
    chat_id: str,
    account_id = "",
    db: AsyncSession = Depends(get_db),
):
    customer_id = request.state.customer_id
    track = f"{customer_id}_{account_id or 0}"
    chat_record = await get_conversation_by_chat_id(db, chat_id)
    if not chat_record:
        logger.warning(f"Chat record not found: {chat_id}")
        raise HTTPException(status_code=404, detail="Chat not found")
    logger.debug(f"Chat access requested for: {chat_id}")
    chats = await get_chats_by_chat_id(chat_id)
    chat_id_data = cache.get(f"chat_context_drawing_{chat_id}") or {}
    if chat_id_data:
        drawing_id = chat_id_data.get("drawing_id")
        metadata = cache.get(f"context_drawing_tool_{track}_{drawing_id}") or {}
        drawing_instructions = metadata.get("drawing_instructions") or {}
        analysis = metadata.get("explanation") or {}
        symbol_metadata = metadata.get("metadata") or {}
        data = {"chat_id": chat_id, "name": chat_record.name, "data": chats, "drawing_instructions": drawing_instructions, "analysis": analysis, "symbol_metadata": symbol_metadata}
    else:
        data = {"chat_id": chat_id, "name": chat_record.name, "data": chats}
    return data


@router.delete("/remove-conversation")
async def remove_conversation_route(
    request: Request, chat_id: str, db: AsyncSession = Depends(get_db)
):
    await remove_conversation_by_chat_id(db, chat_id)
    return {"message": "Chat removed successfully."}


@router.post("/remove-conversations")
async def remove_conversation_route(
    request: Request, chat_ids: list[str], db: AsyncSession = Depends(get_db)
):
    await remove_conversations_by_chat_ids(db, chat_ids)
    return {"message": "Chat removed successfully."}


@router.get("/feedback")
async def feadback_route(
    request: Request,
    chat_id: str,
    feedback: int = Literal[0, 1],
    db: AsyncSession = Depends(get_db),
):
    await give_feedback(chat_id, feedback)
    return JSONResponse(content={"message": "feedback updated successfully"})


@router.put("/rename-conversation")
async def rename_conversation_route(
    request: Request, data: ChatUpdateSchema, db: AsyncSession = Depends(get_db)
):
    await rename_conversation_by_chat_id(db, chat_id=data.chat_id, name=data.name)
    return JSONResponse(content={"message": "chat renamed successfully"})
