import os

import requests
from dotenv import load_dotenv
from fastapi import APIRouter, HTTPException

from app.schemas.sanity_schemas import CategoryResponse, PromptResponse
from app.utils.api_error_handler import with_circuit_breaker
from app.utils.error_handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ExternalAPIError
from app.utils.logger import chatbot_logger as logger

# Load environment variables
load_dotenv()

# Sanity API URL (replace with your actual project ID and dataset)
SANITY_API_URL = os.getenv("SANITY_API_URL")

# Define the GROQ query
QUERY = """
*[_type == "ai_prompts"]{
  title,
  slug,
  mode,
  parent->{
    _id,
    title
  },
  _createdAt,
  _updatedAt
}
"""


router = APIRouter()


@with_circuit_breaker("sanity_api")
def fetch_prompts():
    params = {"query": QUERY}
    try:
        response = requests.get(SANITY_API_URL, params=params, timeout=180)
        response.raise_for_status()  # Raise exception for HTTP errors

        data = response.json()
        return data.get("result", [])

    except requests.exceptions.Timeout:
        logger.error("Sanity API request timed out")
        raise ExternalAPIError(
            service="sanity",
            message="Sanity API request timed out",
            status_code=408
        )
    except requests.exceptions.ConnectionError:
        logger.error("Failed to connect to Sanity API")
        raise ExternalAPIError(
            service="sanity",
            message="Failed to connect to Sanity API",
            status_code=503
        )
    except requests.exceptions.HTTPError as e:
        logger.error(f"Sanity API HTTP error: {e}")
        raise ExternalAPIError(
            service="sanity",
            message=f"Sanity API HTTP error: {e}",
            status_code=e.response.status_code if e.response else 500
        )
    except requests.exceptions.RequestException as e:
        logger.error(f"Sanity API request error: {e}")
        raise ExternalAPIError(
            service="sanity",
            message=f"Sanity API request error: {str(e)}",
            status_code=500
        )
    except Exception as e:
        logger.error(f"Unexpected error fetching prompts: {e}")
        raise ExternalAPIError(
            service="sanity",
            message=f"Unexpected error fetching prompts: {str(e)}",
            status_code=500
        )


@router.get("/prompts", response_model=PromptResponse)
async def get_prompts():
    try:
        prompts = fetch_prompts()
        return {"result": prompts}
    except ExternalAPIError as e:
        return ErrorHandler.create_error_response(e)
    except Exception as e:
        logger.error(f"Unexpected error in get_prompts: {e}")
        error = ExternalAPIError(
            service="sanity",
            message="Failed to fetch prompts",
            status_code=500,
            original_exception=e
        )
        return ErrorHandler.create_error_response(error)


# Define the GROQ query
CATEGORY_QUERY = """
*[_type == "ai_categories"]{
  title,
  slug,
  parent->{
    _id,
    title
  },
  description,
  image,
  _createdAt,
  _updatedAt
}
"""


@with_circuit_breaker("sanity_api")
def fetch_categories():
    params = {"query": CATEGORY_QUERY}
    try:
        response = requests.get(SANITY_API_URL, params=params, timeout=30)
        response.raise_for_status()

        data = response.json()
        return data.get("result", [])

    except requests.exceptions.Timeout:
        logger.error("Sanity API request timed out for categories")
        raise ExternalAPIError(
            service="sanity",
            message="Sanity API request timed out for categories",
            status_code=408
        )
    except requests.exceptions.ConnectionError:
        logger.error("Failed to connect to Sanity API for categories")
        raise ExternalAPIError(
            service="sanity",
            message="Failed to connect to Sanity API for categories",
            status_code=503
        )
    except requests.exceptions.HTTPError as e:
        logger.error(f"Sanity API HTTP error for categories: {e}")
        raise ExternalAPIError(
            service="sanity",
            message=f"Sanity API HTTP error for categories: {e}",
            status_code=e.response.status_code if e.response else 500
        )
    except requests.exceptions.RequestException as e:
        logger.error(f"Sanity API request error for categories: {e}")
        raise ExternalAPIError(
            service="sanity",
            message=f"Sanity API request error for categories: {str(e)}",
            status_code=500
        )
    except Exception as e:
        logger.error(f"Unexpected error fetching categories: {e}")
        raise ExternalAPIError(
            service="sanity",
            message=f"Unexpected error fetching categories: {str(e)}",
            status_code=500
        )


@router.get("/categories", response_model=CategoryResponse)
async def get_categories():
    try:
        categories = fetch_categories()
        return {"result": categories}
    except ExternalAPIError as e:
        return ErrorHandler.create_error_response(e)
    except Exception as e:
        logger.error(f"Unexpected error in get_categories: {e}")
        error = ExternalAPIError(
            service="sanity",
            message="Failed to fetch categories",
            status_code=500,
            original_exception=e
        )
        return ErrorHandler.create_error_response(error)
