import asyncio
import os
import re
import uuid

from fastapi import APIRouter, Depends, File, HTTPException, Request, UploadFile
from fastapi.background import BackgroundTasks
from fastapi.responses import StreamingResponse
from fastapi_pagination import Page, Params, add_pagination
from fastapi_pagination.ext.sqlalchemy import paginate
from openai import OpenAI
from sentence_transformers import SentenceTransformer, util
from sqlalchemy import delete, insert, select
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.config import settings
from app.crud.chat_crud import create_chat, create_conversation
from app.db.database import get_db
from app.middlewares import ai_call_limit, upload_limit, validate_image_file
from app.models.models import ScanImage
from app.rag.vector_database import ChromaDBManager
from app.schemas.chat_schemas import (
    ChartAnalysisRequest,
    ChartAnalysisResult,
    ChartDrawingRequest,
    CombinedChartRequest,
    GenericChatSchema,
    SimplifiedChartRequest_chrome,
    SimplifiedChartRequest_web,
    WebChartAnalysisResult,
    drawing_tool, CombinedChartRequestV2
)
from app.services.chat_with_image_service import (
    download_image,
    encode_image,
    remove_image,
)
from app.services.deep_search_service import deep_search_stream_handler
from app.services.generic_chat_service import (
    analyze_chart_handler,
    analyze_chart_handler_web,
    generic_chat_service,
    handle_image_chat,
    handle_image_chat_stream,
    streaming_generic_chat_service,
)
from app.services.lookalike_storage_service import lookalike_storage
from app.utils import cache
from app.utils.file_utils import (
    TEMP_DIR,
    load_docx_with_langchain,
    load_pdf_with_langchain,
)
from app.utils.general_utils import generate_chat_name
from app.utils.generic_utils import delete_generic_image, save_generic_image
from app.utils.indicator_list import indicators
from app.utils.logger import generic_logger as logger
from app.utils.s3_utils import upload_as_public

client = OpenAI(api_key=settings.OPENAI_API_KEY)


router = APIRouter()
add_pagination(router)


async def create_root_chat(
    db: AsyncSession, account_id: str, question="", chat_type="NORMAL"
):
    chat_id = str(uuid.uuid4())
    parent = chat_id
    await create_conversation(
        db,
        chat_id=chat_id,
        account_id=account_id,
        chat_type=chat_type,
        name=generate_chat_name(question),
    )
    await create_chat(chat_data={"id": chat_id, "chat_id": chat_id, "parent": parent})
    return chat_id, parent


# @router.post("/generic")
# @ai_call_limit
# async def generic(
#     request: Request,
#     data: GenericChatSchema,
#     db: AsyncSession = Depends(get_db),
#     background_tasks: BackgroundTasks = BackgroundTasks(),
# ):
#     """
#     Handles generic chat requests with a question, reply (optional), and account details.
#     Validates inputs, handles chat branching logic, and constructs a user question.
#     Retrieves chat history and checks if the request is an image-based request.
#     If it is, calls the image chat service. Otherwise, calls the generic chat service.
#     Returns the response from the service.
#     """
#     customer_id = request.state.customer_id
#     chat_id = data.chat_id
#     track = f"{customer_id}_{data.account_id or 0}"
#     logger.debug(f"Initializing chat service for customer: {customer_id}")
#     if not data.chat_id:
#         chat_id, parent = await create_root_chat(
#             db, account_id=track, question=data.question
#         )
#         data.chat_id = chat_id
#         data.parent = parent

#     # Validate inputs early
#     if not data.question:
#         raise HTTPException(status_code=400, detail="No question provided")
#     # if not chat_id:
#     #     raise HTTPException(status_code=400, detail="No chat_id provided")
#     # Retrieve cached chat details
#     # chat_detail = cache.get(chat_id)
#     # if chat_detail:
#     #     chat_detail.update(
#     #         {
#     #             "chat_id": chat_id,
#     #             "chat_type": "NORMAL",
#     #             "name": generate_chat_name(data.question),
#     #         }
#     #     )
#     #     chat_detail['account_id'] = track
#     #     await create_chat_record(db, ChatModelSchema(**chat_detail))
#     #     cache.delete(chat_id)
#     # logger.info(f"Chat details: {chat_detail}")

#     # Handle edited questions (branching logic)
#     # chat_id = await handle_chat_branching(db, data, track, chat_id)

#     # Construct user question
#     user_question = (
#         f"Reply to: {data.reply}\n\nQuestion: {data.question}"
#         if data.reply
#         else data.question
#     )
#     if data.symbol:
#         user_question += f" for Symbol: {data.symbol}"
#     if data.selected_category:
#         if data.selected_category.lower() == "indicators":
#             user_question += f" for Indicator: {data.selected_option}"
#         elif data.selected_category.lower() == "data":
#             user_question += f" for Catogory: {data.selected_option} For data: {data.selected_sub_option}"
#         else:
#             user_question += f" for Catogory: {data.selected_option} For symbol: {data.selected_sub_option}"

#     # if data.selected_category:
#     #     user_question += f" for indicator: {data.selected_option}"

#     logger.info(
#         f"User Question: {user_question} | Websearch: {data.is_websearch_enabled}"
#     )

#     # Retrieve chat history
#     history_key = f"chat_history_{chat_id}"
#     generic_chat_history = cache.get(history_key) or []
#     cache.set(history_key, generic_chat_history)

#     # Handle image-based chat requests
#     images = data.hidden_images or data.images or data.chat_images
#     if images:
#         image_response = await handle_image_chat(
#             data, user_question, track, generic_chat_history, chat_id, db=db
#         )
#         if image_response:
#             return (
#                 image_response  # If image chat response exists, return it immediately
#             )

#     # Fallback to generic chat service
#     logger.info(f"Going into generic chat service: {chat_id}")
#     response = await generic_chat_service(
#         db,
#         chat_id,
#         data.account_id,
#         data.trading_token,
#         generic_chat_history=generic_chat_history,
#         is_websearch_enabled=data.is_websearch_enabled,
#         question=user_question,
#         reply=data.reply,
#         token=data.token,
#         track=track,
#         selected_category=data.selected_category,
#         selected_option=data.selected_option,
#         background_tasks=background_tasks,
#         parent=data.parent,
#         edited_question=data.edited_question,
#     )

#     return response  # Final response if no image-based response was found


@router.post("/upload-generic-image")
@upload_limit(file_type="generic_image")
async def upload_generic_image_route(
    request: Request,
    account_id: str = "",
    file: UploadFile = Depends(validate_image_file),
):  # File upload 1 MB limit
    """
    Uploads a generic image to S3.

    Args:
    account_id (str): The account ID to associate with the image. Defaults to an empty string.
    file (UploadFile): The image file to upload.

    Returns:
    dict: Containing the image path.
    """
    if account_id:
        account_id = f"{request.state.customer_id}_{account_id}"
    else:
        account_id = f"{request.state.customer_id}_0"
    file_name = f"{account_id}/generic_images"
    image_path = await save_generic_image(file_name, file)
    logger.debug(f"Generic-Image saved: {image_path}")
    return {"image_path": image_path}


@router.post("/upload-document")
@upload_limit(file_type="document")
async def upload_document_route(
    request: Request,
    chat_id: str = "",
    account_id: str = "",
    file: UploadFile = File(...),
    db: AsyncSession = Depends(get_db),
):
    """
    Uploads a document (PDF or DOCX) and embeds it in ChromaDB for context-aware chat.

    Args:
        request (Request): The request object
        chat_id (str): The chat ID to associate the document with
        account_id (str): The account ID to associate with the document. Defaults to empty string.
        file (UploadFile): The document file (PDF or DOCX)
        db (AsyncSession): Database session

    Returns:
        dict: Status of the upload and embedding process
    """
    if account_id:
        account_id = f"{request.state.customer_id}_{account_id}"
    else:
        account_id = f"{request.state.customer_id}_0"

    filename = file.filename
    extension = filename.split(".")[-1].lower()
    if extension not in ["pdf", "docx"]:
        raise HTTPException(
            status_code=400,
            detail="Invalid file format. Only PDF and DOCX files are supported.",
        )
    # set a limit based on file_paths count that only 3 files can be uploaded
    context_data = cache.get(f"chat_context_{account_id}") or {}
    file_paths = context_data.get("file_paths", [])
    if len(file_paths) >= 3:
        raise HTTPException(
            status_code=400, detail="You can only upload 3 files at a time."
        )
    # check if chat_id exists in cache
    if not chat_id:
        context_chat_key = f"context_chat_key_{account_id}"
        context_chat = cache.get(context_chat_key) or {}
        chat_id = context_chat.get("chat_id")
    if not chat_id:
        # create_root_chat
        chat_id, parent = await create_root_chat(db, account_id)
        context_chat_key = f"context_chat_key_{account_id}"
        cache.set(context_chat_key, {"chat_id": chat_id, "parent": parent})

    # Save file to S3
    file_name = f"{account_id}/documents/{chat_id}_{filename}"
    content = await file.read()

    # also save to s3 as public
    url = await upload_as_public(path=file_name, file=file)

    # Process file content
    try:
        # Create temp file and get documents
        os.makedirs(TEMP_DIR, exist_ok=True)
        temp_file_path = os.path.join(TEMP_DIR, f"{account_id}_{filename}")

        with open(temp_file_path, "wb") as temp_file:
            temp_file.write(content)
        # Get documents based on file type
        if extension == "pdf":
            documents = load_pdf_with_langchain(temp_file_path)
        elif extension == "docx":
            documents = load_docx_with_langchain(temp_file_path)

        # Store in ChromaDB
        db_manager = ChromaDBManager()
        db_manager.store_document(chat_id, filename, documents)

        # Cache the context information
        context_key = f"chat_context_{chat_id}"
        # check if it's arlready exist and make file_paths a list if it's there already and append
        saved_context_data = cache.get(context_key)
        if saved_context_data and "file_paths" in saved_context_data:
            saved_context_data["file_paths"].append(url)
            cache.set(context_key, saved_context_data)
        else:
            cache.set(
                context_key,
                {
                    "has_context": True,
                    "document_name": filename,
                    "document_type": extension,
                    "file_paths": [url],
                },
            )
        context_key = f"chat_context_saved_{chat_id}"
        cache.set(context_key, url)

        # Clean up temp file
        if os.path.exists(temp_file_path):
            os.remove(temp_file_path)

        logger.info(f"Document uploaded and embedded for chat: {chat_id}")
        return {
            "status": "success",
            "message": "Document uploaded and embedded successfully",
            "document_name": filename,
            "file_path": url,
        }

    except Exception as e:
        logger.error(f"Error processing document: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500, detail=f"Error processing document: {str(e)}"
        )


@router.post("/upload-scanned-image")
@upload_limit(file_type="scanned_image")
async def upload_scanned_image_route(
    request: Request,
    account_id: str = "",
    file: UploadFile = Depends(validate_image_file),
    db: AsyncSession = Depends(get_db),
):
    """
    Uploads a scanned image to S3 and saves it to the database.

    Args:
    account_id (str): The account ID to associate with the image. Defaults to an empty string.
    hidden (bool): Whether to hide the image in the database, or not. Defaults to False.
    file (UploadFile): The image file to upload.

    Returns:
    dict: Containing the image path.
    """
    if account_id:
        account_id = f"{request.state.customer_id}_{account_id}"
    else:
        account_id = f"{request.state.customer_id}_0"
    file_name = f"{account_id}/scanned_images"
    image_path = await save_generic_image(file_name, file)
    logger.debug(f"Scanned-Image saved: {image_path}")
    return {"image_path": image_path}


@router.get("/get-scanned-images", response_model=Page[dict])
async def get_scanned_images(
    request: Request,
    account_id: str = "",
    params: Params = Depends(),
    db: AsyncSession = Depends(get_db),
):
    """
    Get paginated list of scanned images for the given account_id.

    Args:
    request (Request): The request sent to the API.
    account_id (str): The ID of the account. Defaults to "".
    params (Params): The query parameters. Defaults to Params().
    db (AsyncSession): The database session. Defaults to Depends(get_db).

    Returns:
    Page[dict]: A paginated list of scanned images.
    """
    if account_id:
        account_id = f"{request.state.customer_id}_{account_id}"
    else:
        account_id = f"{request.state.customer_id}_0"

    stmt = (
        select(ScanImage)
        .where(ScanImage.account == account_id, ScanImage.type == "mobile")
        .order_by(ScanImage.created_at.desc())
    )

    paginated_result = await paginate(db, stmt, params)
    paginated_result.items = [
        {
            "id": image.id,
            "account": image.account,
            "file_name": image.file_name,
            "extension": image.extension,
            "file_path": image.file_path,
            "created_at": image.created_at,
            "updated_at": image.updated_at,
            # "hidden_file_path": image.hidden_file_path,
            "bearish_strength": image.bearish_strength,
            "bullish_strength": image.bullish_strength,
            "price": image.price,
            "date_range": image.date_range,
            "time_frame": image.time_frame,
            "currency": image.currency,
            "title": image.title,
            # "market_overview": image.market_overview,
            # "technical_analysis": image.technical_analysis,
            # "event_activity": image.event_activity,
            "analyses": [
                {
                    "title": "Market Overview",
                    "analysis": image.market_overview,
                },
                {
                    "title": "Technical Analysis",
                    "analysis": image.technical_analysis,
                },
                {
                    "title": "Event Activity",
                    "analysis": image.event_activity,
                },
            ],
        }
        for image in paginated_result.items
    ]
    logger.info(f"Scanned-Images fetched: {paginated_result.items}")
    return paginated_result


@router.get("/get-scanned-images-web", response_model=Page[dict])
async def get_scanned_images(
    request: Request,
    account_id: str = "",
    params: Params = Depends(),
    db: AsyncSession = Depends(get_db),
):
    """
    Get paginated list of scanned images with associated lookalike data for the given account_id.

    This endpoint returns both chart analysis data and lookalike search results
    using intelligent matching by account + vuelensid/lens_id for a unified view.

    Matching Strategy:
    1. Primary: Match by account + vuelensid (unified lens system)
    2. Fallback: Match by account + lens_id (legacy records)
    3. Efficient: Single query for all lookalike data, then in-memory matching

    Args:
    request (Request): The request sent to the API.
    account_id (str): The ID of the account. Defaults to "".
    params (Params): The query parameters for pagination. Defaults to Params().
    db (AsyncSession): The database session. Defaults to Depends(get_db).

    Returns:
    Page[dict]: A paginated list of scanned images with lookalike data included.
    Each item contains:
    - Analysis data (bullish/bearish strength, technical analysis, etc.)
    - Lookalike data (search results, similar charts) matched by account + ID
    """
    if account_id:
        account_id = f"{request.state.customer_id}_{account_id}"
    else:
        account_id = f"{request.state.customer_id}_0"

    stmt = (
        select(ScanImage)
        .where(ScanImage.account == account_id, ScanImage.type == "web")
        .order_by(ScanImage.created_at.desc())
    )

    paginated_result = await paginate(db, stmt, params)

    # Get all lookalike data for this account first (more efficient)
    all_lookalike_data = await lookalike_storage.get_user_search_history(
        db=db, account=account_id, limit=100  # Get more to match with analysis
    )

    # Create a lookup dictionary for faster matching
    lookalike_lookup = {}
    for lookalike in all_lookalike_data:
        # Index by both vuelensid and search_id for flexible matching
        if lookalike.get("vuelensid"):
            lookalike_lookup[lookalike["vuelensid"]] = lookalike
        lookalike_lookup[lookalike["search_id"]] = lookalike

    # Enhance each item with lookalike data
    enhanced_items = []
    for image in paginated_result.items:
        # Try to find matching lookalike data
        lookalike_data = None

        # Strategy 1: Match by vuelensid
        if image.vuelensid and image.vuelensid in lookalike_lookup:
            lookalike_data = lookalike_lookup[image.vuelensid]

        # Strategy 2: Match by lens_id (fallback for older records)
        elif image.lens_id and image.lens_id in lookalike_lookup:
            lookalike_data = lookalike_lookup[image.lens_id]

        enhanced_item = {
            "id": image.id,
            "account": image.account,
            "lens_id": image.lens_id or "",
            "vuelensid": image.vuelensid or "",
            "file_name": image.file_name,
            "extension": image.extension,
            "file_path": image.file_path,
            "created_at": image.created_at,
            "updated_at": image.updated_at,
            "hidden_file_path": image.hidden_file_path,
            "bearish_strength": image.bearish_strength,
            "bullish_strength": image.bullish_strength,
            "price": image.price,
            "date_range": image.date_range,
            "time_frame": image.time_frame,
            "currency": image.currency,
            "title": image.title,
            "symbol": image.symbol,
            "symbol_logo": image.symbol_logo,
            "analyses": [
                {
                    "title": "Market Overview",
                    "analysis": image.market_overview,
                },
                {
                    "title": "Candlesticks Overview",
                    "analysis": image.candlestick_overview,
                },
                {
                    "title": "Technical Analysis",
                    "analysis": image.technical_analysis,
                },
                {
                    "title": "Event Activity",
                    "analysis": image.event_activity,
                },
            ],
            "lookalike": lookalike_data,  # Add lookalike data
        }
        enhanced_items.append(enhanced_item)

    paginated_result.items = enhanced_items
    logger.info(f"Scanned-Images fetched: {paginated_result.items}")
    return paginated_result


@router.delete("/delete-scanned-image")
async def delete_scanned_image(image_id: int, db: AsyncSession = Depends(get_db)):
    """
    Deletes a scanned image from the database and storage.

    Args:
    image_id (int): The ID of the image to delete.
    db (AsyncSession): The database session.

    Returns:
    dict: A message indicating the result of the deletion operation.
    If the image is not found, returns a message indicating that the image was not found.
    """

    image = await db.get(ScanImage, image_id)
    if not image:
        return {"message": "Image not found"}
    await delete_generic_image(image.file_path)
    await delete_generic_image(image.hidden_file_path)
    stmt = delete(ScanImage).where(ScanImage.id == image_id)
    await db.execute(stmt)
    await db.commit()
    return {"message": "Image deleted"}


@router.post("/delete-scanned-images")
async def delete_scanned_images(
    image_ids: list[int], account_id: str = "", db: AsyncSession = Depends(get_db)
):
    for image_id in image_ids:
        image = await db.get(ScanImage, image_id)
        if not image:
            return {"message": "Image not found"}
        await delete_generic_image(image.file_path)
        stmt = delete(ScanImage).where(ScanImage.id == image_id)
        await db.execute(stmt)
    await db.commit()
    return {"message": "Images deleted"}


@router.post("/analyze-chart")
@ai_call_limit
async def analyze_chart(
    request: Request, data: ChartAnalysisRequest, db: AsyncSession = Depends(get_db)
):
    """
    Analyze a chart image using the LLM.

    Args:
    request (Request): The request sent to the API.
    data (ChartAnalysisRequest): The request data containing the account_id, images and other information.
    db (AsyncSession): The database session.

    Returns:
    ChartAnalysisResult: The analysis result of the chart as a JSON object.

    Raises:
    HTTPException: If the image is not found or if the analysis fails.
    """
    logger.info(f"Chart analysis request: {data}")
    images_data = []
    if data.account_id:
        account_id = f"{request.state.customer_id}_{data.account_id}"
    else:
        account_id = f"{request.state.customer_id}_0"
    data.account_id = account_id

    # Download and encode images
    try:
        for image in data.images:
            image_path, extension = download_image(image, data.account_id)
            encoded_image = encode_image(str(image_path))
            images_data.append(
                {
                    "image_url": f"data:image/{extension};base64,{encoded_image}",
                    "image_path": image,
                }
            )
    except Exception as e:
        logger.error(f"Image processing failed: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=400, detail=f"Failed to process image: {str(e)}"
        )

    # Prepare content for the LLM
    content = [
        {"type": "image_url", "image_url": {"url": img["image_url"]}}
        for img in images_data
    ]

    # Define the system message based on whether the symbol is provided
    if not data.symbol:
        system_message = """
        Analyze the image and return a JSON response with the following structure:
        - If the image is a candlestick chart, provide bullish strength, bearish strength, stock price, and date range.
        - If any information is unclear, set `clarification_needed` to true and list the missing data in `data_needed`.
        - If the image is not a candlestick chart, set `error` to "not a candlestick chart".

        Example Response 1:
        {
            "symbol": "AAPL",
            "timeframe": "1d",
            "date_range": "23 Dec - 26 Dec",
            "Bullish Strength": "65%",
            "Bearish Strength": "35%",
            "stock_price": "187.83",
            "currency": "USD",
            "clarification_needed": false,
            "data_needed": [],
            "error": "",
            "title": "<title for the chart analysis>"
        }

        Example Response 2:
        {
            "symbol": "",
            "timeframe": "",
            "date_range": "",
            "Bullish Strength": "65%",
            "Bearish Strength": "35%",
            "stock_price": "",
            "currency": "USD",
            "clarification_needed": true,
            "data_needed": ["symbol", "timeframe", "date_range", "stock_price"],
            "error": "",
            "title": ""
        },

        Example Response 3:
        {
            "symbol": "",
            "timeframe": "",
            "date_range": "",
            "Bullish Strength": "",
            "Bearish Strength": "",
            "stock_price": "",
            "currency": "USD",
            "clarification_needed": false,
            "data_needed": [],
            "error": "not a candlestick chart",
            "title": ""
        }
        """
    else:
        system_message = f"""
        Analyze the image and return a JSON response with the following structure:
        Known Information:
        - Symbol: "{data.symbol}"
        - Timeframe: "{data.timeframe}"
        - Date Range: "{data.date_range}"
        - Stock Price: "{data.stock_price}"


        Note:
        - If any information is unclear, set `clarification_needed` to true and list the missing data in `data_needed`.
        - Use Known Information to guide the analysis. respond with only clear information either from the image or from the known information.
        - If image is not clear or the Known information is missing or blank set `clarification_needed` to true and list the missing data in `data_needed`.
        - never add bullish or bearish strength to data_needed. those should be predicted from the image.
        - If the image is not a candlestick chart, set `error` to "not a candlestick chart".

        Example Response 1:
        {{
            "symbol": "EURUSD",
            "timeframe": "1m",
            "date_range": "26 Dec - 28 Dec",
            "Bullish Strength": "65%",
            "Bearish Strength": "35%",
            "stock_price": "187.83",
            "currency": "USD",
            "clarification_needed": false,
            "data_needed": [],
            "title": "<title for analysis>",
            "error": ""
        }}
        Example Response 2:
        {{
            "symbol": "EURUSD",
            "timeframe": "15m",
            "date_range": "23 Dec - 26 Dec",
            "Bullish Strength": "65%",
            "Bearish Strength": "35%",
            "stock_price": "",
            "currency": "USD",
            "clarification_needed": true,
            "data_needed": ["stock_price"],
            "title": "",
        }}
        Example Response 3:
        {{
            "symbol": "",
            "timeframe": "",
            "date_range": "",
            "Bullish Strength": "",
            "Bearish Strength": "",
            "stock_price": "",
            "currency": "USD",
            "clarification_needed": false,
            "data_needed": [],
            "error": "not a candlestick chart"
            "title": "",
        }}
        """

    # Call the LLM
    try:
        response = client.beta.chat.completions.parse(
            model=settings.MODEL_NAME,
            messages=[
                {"role": "system", "content": system_message},
                {"role": "user", "content": content},
            ],
            response_format=ChartAnalysisResult,
        )
        response = response.choices[0].message.parsed
    except Exception as e:
        logger.error(f"Chart Analyzing failed: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500, detail=f"Failed to analyze chart: {str(e)}"
        )

    # Clean up images
    finally:
        for image in images_data:
            remove_image(image["image_path"])

    # Add static prompts to the response
    static_prompts = [
        {
            "title": "General Overview",
            "prompt": "Provide a general overview of the chart.",
        },
        {
            "title": "Market Structure",
            "prompt": "Provide an analysis of the overall market structure.",
        },
        {
            "title": "Trend Overview",
            "prompt": "Provide information about the general trend.",
        },
        {
            "title": "Technical Overview",
            "prompt": "Provide information about technical indicators.",
        },
        {
            "title": "Event Activity",
            "prompt": "Provide information about events in this time period.",
        },
    ]

    async def call_llm(image_url: str, system_msg: str, response_model=None):
        """Helper function to send a request to the LLM model."""
        try:
            messages = [
                {"role": "system", "content": system_msg},
                {
                    "role": "user",
                    "content": [{"type": "image_url", "image_url": {"url": image_url}}],
                },
            ]

            if response_model:
                response = client.beta.chat.completions.parse(
                    model=settings.MODEL_NAME,
                    messages=messages,
                    response_format=response_model,
                )
                return response.choices[0].message.parsed.model_dump()
            else:
                response = client.chat.completions.create(
                    model=settings.MODEL_NAME,
                    messages=messages,
                )
                return response.choices[0].message.content
        except Exception as e:
            logger.error(f"LLM call failed: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=500, detail=f"LLM processing failed: {str(e)}"
            )

    response = response.model_dump()

    async def get_analysis(prompt):
        try:
            system_msg = f"""You are a professional financial analyst specializing in technical analysis.
            Provide detailed but concise analysis (2-3 paragraphs) focusing specifically on {prompt['title']}."""

            analysis_text = await call_llm(
                images_data[0]["image_url"], system_msg + "\n\n" + prompt["prompt"]
            )

            return {"title": prompt["title"], "analysis": analysis_text}
        except Exception as e:
            logger.error(f"Failed to generate analysis for {prompt['title']}: {str(e)}")
            return {
                "title": prompt["title"],
                "analysis": f"Analysis generation failed for this section. Error: {str(e)}",
            }

    analysis_prompts = [
        {
            "title": "Market Overview",
            "prompt": f"Provide a comprehensive analysis of the {response.get('symbol', 'chart')} on {response.get('timeframe', '')} timeframe from {response.get('date_range', 'the shown period')}. "
            "Focus on market structure, key price levels, and overall price action. "
            "Identify support/resistance levels and market phases. "
            "If image is blank, indicate analysis not possible.",
        },
        {
            "title": "Technical Analysis",
            "prompt": f"Analyze the technical aspects of {response.get('symbol', 'this asset')} with bullish strength at {response.get('bullish_strength', 0)}% "
            f"and bearish strength at {response.get('bearish_strength', 0)}%. "
            "Include trend analysis, key indicators (RSI, MAs, volume), and potential entry/exit points. "
            "Identify trend strength and potential reversal signals. "
            "If image is blank, indicate analysis not possible.",
        },
        {
            "title": "Event Activity",
            "prompt": f"Analyze significant price movements and events for {response.get('symbol', '')} during {response.get('date_range', 'this period')}. "
            "Focus on volume spikes, price gaps, and sudden movements that might indicate market events or news impact. "
            "If image is blank, indicate analysis not possible.",
        },
    ]
    # Run all analyses in parallel
    analyses = await asyncio.gather(
        *[get_analysis(prompt) for prompt in analysis_prompts]
    )

    response["prompts"] = static_prompts
    stmt = insert(ScanImage).values(
        account=data.account_id,
        file_path=data.images[0],
        # hidden_file_path=data.images[1],
        bearish_strength=response.get("bearish_strength"),
        bullish_strength=response.get("bullish_strength"),
        price=response.get("stock_price"),
        date_range=response.get("date_range"),
        time_frame=response.get("timeframe"),
        symbol=response.get("symbol"),
        currency=response.get("currency"),
        title=response.get("title"),
        market_overview=analyses[0]["analysis"],
        technical_analysis=analyses[1]["analysis"],
        event_activity=analyses[2]["analysis"],
        type="mobile",
    )
    result = await db.execute(stmt)
    response["id"] = result.lastrowid
    response["analyses"] = analyses
    await db.commit()

    if response.get("error"):
        raise HTTPException(status_code=400, detail=response)

    return response


# @router.post("/analyze-chart-web")
# async def analyze_chart(
#     request: Request,
#     data: SimplifiedChartRequest_web,
#     db: AsyncSession = Depends(get_db),
# ):
#     logger.info(f"Web-Chart analysis request: {data}")
#     images_data = []

#     if data.account_id:
#         account_id = f"{request.state.customer_id}_{data.account_id}"
#     else:
#         account_id = f"{request.state.customer_id}_0"

#     data.account_id = account_id

#     # Ensure we have exactly two images
#     if len(data.images) < 2:
#         raise HTTPException(status_code=400, detail="Exactly 2 images are required.")

#     # Download and encode both images
#     try:
#         for image in data.images[:2]:  # Only process the first 2 images
#             image_path, extension = download_image(image, data.account_id)
#             encoded_image = encode_image(str(image_path))
#             images_data.append(
#                 {
#                     "image_url": f"data:image/{extension};base64,{encoded_image}",
#                     "image_path": image,
#                 }
#             )
#     except Exception as e:
#         logger.error(f"Image processing failed: {str(e)}", exc_info=True)
#         raise HTTPException(
#             status_code=400, detail=f"Failed to process image: {str(e)}"
#         )

#     # System message for initial chart analysis
#     system_message = """
#     Analyze the given financial chart image and return JSON with:
#     - symbol (if detectable on top left of the image)
#     - timeframe (if visible on the right of the symbol in image)
#     - date_range (On the below of the image - must be in "from - to" format, e.g. "23 Dec - 26 Dec") and if 1st image is blank then let it be blank
#     - bullish_strength and bearish_strength (0-100) and trend from the first image only if that image is blank then let it be 0 or null
#     - properly give bullish_strength and bearish_strength by checking candles and getting trend
#     - stock_price (current price from the second image, numeric value only)
#     - currency (3-letter code like if pairs(EURUSD, SOLUSD) then last 3 letters if available, otherwise leave empty)
#     - title for the analysis

#     For stock_price:
#     - Extract the exact numeric value shown as current price
#     - Ignore any currency symbols or text
#     - Format as number with decimal point if needed (e.g. 187.83)
#     - If unavailable, use 0

#     Example Response:
#     {
#         "symbol": "AAPL",
#         "timeframe": "1d",
#         "date_range": "23 Dec - 26 Dec",
#         "bullish_strength": 65,
#         "bearish_strength": 35,
#         "stock_price": 187.83,
#         "currency": "USD",
#         "title": "Apple Inc. Daily Chart Analysis"
#     }
#     """

#     async def call_llm(image_url: str, system_msg: str, response_model=None):
#         """Helper function to send a request to the LLM model."""
#         try:
#             messages = [
#                 {"role": "system", "content": system_msg},
#                 {
#                     "role": "user",
#                     "content": [{"type": "image_url", "image_url": {"url": image_url}}],
#                 },
#             ]

#             if response_model:
#                 response = client.beta.chat.completions.parse(
#                     model=settings.MODEL_NAME,
#                     messages=messages,
#                     response_format=response_model,
#                 )
#                 return response.choices[0].message.parsed.model_dump()
#             else:
#                 response = client.chat.completions.create(
#                     model=settings.MODEL_NAME,
#                     messages=messages,
#                 )
#                 return response.choices[0].message.content
#         except Exception as e:
#             logger.error(f"LLM call failed: {str(e)}", exc_info=True)
#             raise HTTPException(
#                 status_code=500, detail=f"LLM processing failed: {str(e)}"
#             )

#     def safe_convert_int(value, default=0):
#         try:
#             return int(float(value))
#         except (ValueError, TypeError):
#             return default

#     def format_date_range(date_range: str) -> str:
#         """Format date range to ensure consistent 'from - to' format"""
#         if not date_range:
#             return ""

#         # Remove any extra spaces around the dash
#         date_range = date_range.strip()
#         date_range = re.sub(r"\s*-\s*", " - ", date_range)

#         # Ensure proper spacing and formatting
#         parts = date_range.split(" - ")
#         if len(parts) == 2:
#             return f"{parts[0].strip()} - {parts[1].strip()}"
#         return date_range

#     def convert_response(response_data: dict, first_image_data: dict) -> dict:
#         """Merge data, giving priority to first image for strength values and trend."""
#         bullish = safe_convert_int(first_image_data.get("bullish_strength", 0))
#         bearish = safe_convert_int(first_image_data.get("bearish_strength", 0))

#         stock_price = response_data.get("stock_price", "0")
#         currency = response_data.get("currency", "")

#         # Clean and format currency (3-letter code or empty)
#         currency = currency[:3].upper() if currency and len(currency) >= 3 else ""

#         # Format date range
#         date_range = format_date_range(response_data.get("date_range", ""))

#         return {
#             "bullish_strength": bullish,
#             "bearish_strength": bearish,
#             "stock_price": stock_price,
#             "currency": currency,
#             "date_range": date_range,
#             "timeframe": response_data.get("timeframe", ""),
#             "symbol": response_data.get("symbol", ""),
#             "title": response_data.get("title", ""),
#         }

#     try:
#         # Call LLM for both images in parallel
#         response_data_1, response_data_2 = await asyncio.gather(
#             call_llm(
#                 images_data[0]["image_url"], system_message, WebChartAnalysisResult
#             ),
#             call_llm(
#                 images_data[1]["image_url"], system_message, WebChartAnalysisResult
#             ),
#         )

#         # Merge the results
#         merged_data = convert_response(response_data_2, response_data_1)

#         # Define optimized analysis prompts
#         analysis_prompts = [
#             {
#                 "title": "Market Overview",
#                 "prompt": f"Provide a comprehensive analysis of the {merged_data.get('symbol', 'chart')} on {merged_data.get('timeframe', '')} timeframe from {merged_data.get('date_range', 'the shown period')}. "
#                 "Focus on market structure, key price levels, and overall price action. "
#                 "Identify support/resistance levels and market phases. "
#                 "If image is blank, indicate analysis not possible.",
#             },
#             {
#                 "title": "Technical Analysis",
#                 "prompt": f"Analyze the technical aspects of {merged_data.get('symbol', 'this asset')} with bullish strength at {merged_data.get('bullish_strength', 0)}% "
#                 f"and bearish strength at {merged_data.get('bearish_strength', 0)}%. "
#                 "Include trend analysis, key indicators (RSI, MAs, volume), and potential entry/exit points. "
#                 "Identify trend strength and potential reversal signals. "
#                 "If image is blank, indicate analysis not possible.",
#             },
#             {
#                 "title": "Event Activity",
#                 "prompt": f"Analyze significant price movements and events for {merged_data.get('symbol', '')} during {merged_data.get('date_range', 'this period')}. "
#                 "Focus on volume spikes, price gaps, and sudden movements that might indicate market events or news impact. "
#                 "If image is blank, indicate analysis not possible.",
#             },
#         ]

#         # Generate analyses for each prompt in parallel
#         async def get_analysis(prompt):
#             try:
#                 system_msg = f"""You are a professional financial analyst specializing in technical analysis.
#                 Provide detailed but concise analysis (2-3 paragraphs) focusing specifically on {prompt['title']}."""

#                 analysis_text = await call_llm(
#                     images_data[0]["image_url"], system_msg + "\n\n" + prompt["prompt"]
#                 )

#                 return {"title": prompt["title"], "analysis": analysis_text}
#             except Exception as e:
#                 logger.error(
#                     f"Failed to generate analysis for {prompt['title']}: {str(e)}"
#                 )
#                 return {
#                     "title": prompt["title"],
#                     "analysis": f"Analysis generation failed for this section. Error: {str(e)}",
#                 }

#         # Run all analyses in parallel
#         analyses = await asyncio.gather(
#             *[get_analysis(prompt) for prompt in analysis_prompts]
#         )

#     except Exception as e:
#         logger.error(f"Chart analysis failed: {str(e)}", exc_info=True)
#         raise HTTPException(status_code=500, detail=f"Chart analysis failed: {str(e)}")
#     finally:
#         # Clean up images
#         for image in images_data:
#             remove_image(image["image_path"])

#     # Prepare final response
#     final_response = {**merged_data, "analyses": analyses}

#     try:
#         stmt = insert(ScanImage).values(
#             account=data.account_id,
#             file_path=data.images[0],
#             hidden_file_path=data.images[1],
#             bearish_strength=merged_data["bearish_strength"],
#             bullish_strength=merged_data["bullish_strength"],
#             price=merged_data["stock_price"],
#             date_range=merged_data["date_range"],
#             time_frame=merged_data["timeframe"],
#             currency=merged_data["currency"],
#             title=merged_data["title"],
#             market_overview=analyses[0]["analysis"],
#             technical_analysis=analyses[1]["analysis"],
#             event_activity=analyses[2]["analysis"],
#             symbol=merged_data["symbol"],
#             symbol_logo=data.symbol,
#             type="web",
#         )
#         result = await db.execute(stmt)
#         final_response["id"] = result.lastrowid
#         await db.commit()
#     except Exception as e:
#         logger.error(f"Database save failed: {str(e)}", exc_info=True)
#         final_response["db_save_error"] = str(e)

#     return final_response


@router.post("/analyze-chart-web-v2")
async def analyze_chart(
    request: Request,
    payload: CombinedChartRequestV2,
    vuelensid: str = "",
    db: AsyncSession = Depends(get_db),
):
    logger.info(f"Account ID: {payload.data.account_id}")
    logger.info(f"Chart Data Points: {len(payload.chart.chartData)}")
    logger.info(f"Vue Lens ID: {vuelensid}")

    return StreamingResponse(
        analyze_chart_handler_web(
            request=request,
            data=payload.data,
            chart=payload.chart,
            vuelensid=vuelensid,
            db=db,
        ),
        media_type="text/event-stream",
        headers={
            "Content-Type": "text/event-stream",
            "Cache-Control": "no-store",
            "Connection": "keep-alive",
            "Transfer-Encoding": "chunked",
        },
    )


@router.post("/analyze-chart-chrome-v2")
async def analyze_chart(
    request: Request,
    data: SimplifiedChartRequest_chrome,
    db: AsyncSession = Depends(get_db),
):
    logger.info(f"Web-Chart analysis request: {data}")
    return StreamingResponse(
        analyze_chart_handler(request=request, data=data, db=db),
        media_type="text/event-stream",
    )


@router.post("/generic-v2")
# @ai_call_limit
async def generic(
    request: Request,
    data: GenericChatSchema,
    params: Params = Depends(),
    db: AsyncSession = Depends(get_db),
    background_tasks: BackgroundTasks = BackgroundTasks(),
):
    """
    Handles generic chat requests with a question, reply (optional), and account details.
    Validates inputs, handles chat branching logic, and constructs a user question.
    Retrieves chat history and checks if the request is an image-based request.
    If it is, calls the image chat service. Otherwise, calls the generic chat service.
    Returns the response from the service.

    Supports both single selection and multiple selections for comparative analysis.
    """
    customer_id = request.state.customer_id
    chat_id = data.chat_id
    track = f"{customer_id}_{data.account_id or 0}"
    context_chat_key = f"context_chat_key_{track}"
    context_chat = cache.get(context_chat_key) or {}
    if context_chat:
        data.chat_id = context_chat.get("chat_id")
        chat_id = data.chat_id
        data.parent = context_chat.get("parent")
        cache.delete(context_chat_key)

    logger.debug(f"Initializing chat service for customer: {customer_id}")
    if not data.chat_id:
        chat_id, parent = await create_root_chat(
            db, account_id=track, question=data.question
        )
        data.chat_id = chat_id
        data.parent = parent

    # Validate inputs early
    if not data.question:
        raise HTTPException(status_code=400, detail="No question provided")
    if not chat_id:
        raise HTTPException(status_code=400, detail="No chat_id provided")

    # Construct user question
    user_question = (
        f"Reply to: {data.reply}\n\nQuestion: {data.question}"
        if data.reply
        else data.question
    )

    # Add symbol information if provided
    if data.symbol:
        user_question += f" for Symbol: {data.symbol}"

    # Handle multiple selections for comparative analysis
    if data.selections and len(data.selections) > 0:
        user_question += " for comparative analysis of the following items:"

        for i, selection in enumerate(data.selections):
            if selection.category.lower() == "indicators":
                user_question += f"\n{i+1}. Indicator: {selection.option}"
            elif selection.category.lower() == "data":
                user_question += f"\n{i+1}. Category: {selection.option}, Data: {selection.sub_option}"
            else:  # Symbols
                user_question += f"\n{i+1}. Category: {selection.option}, Symbol: {selection.sub_option}"

        # Make it very clear this is a comparative analysis request
        user_question += "\n\nPlease provide a detailed comparison of these items."

        logger.info(f"Constructed comparative analysis question: {user_question}")

    # Handle single selection (backward compatibility)
    elif data.selected_category:
        if data.selected_category.lower() == "indicators":
            user_question += f" for Indicator: {data.selected_option}"
        elif data.selected_category.lower() == "data":
            user_question += f" for Category: {data.selected_option} For data: {data.selected_sub_option}"
        else:
            user_question += f" for Catogory: {data.selected_option} For symbol: {data.selected_sub_option}"

    logger.info(
        f"User Question: {user_question} | Websearch: {data.is_websearch_enabled}"
    )

    # Retrieve chat history
    history_key = f"chat_history_{chat_id}"
    generic_chat_history = cache.get(history_key) or []
    cache.set(history_key, generic_chat_history)

    # Handle image-based chat requests
    images = data.hidden_images or data.images or data.chat_images
    if images:
        return StreamingResponse(
            handle_image_chat_stream(
                data, params, user_question, track, generic_chat_history, chat_id, db=db
            ),
            media_type="text/event-stream",
            headers={
                "Content-Type": "text/event-stream",
                "Cache-Control": "no-store",
                "Connection": "keep-alive",
                "Transfer-Encoding": "chunked",
            },
        )

    # Process with generic chat service
    logger.info(f"Going into generic chat service: {chat_id}")

    # Check if deep search is requested
    if data.is_deep_search:
        logger.info(f"Deep search requested for question: {user_question}")
        return StreamingResponse(
            deep_search_stream_handler(
                db,
                chat_id,
                data.account_id,
                user_question,
                generic_chat_history,
                reply=data.reply,
                track=track,
                parent=data.parent,
            ),
            media_type="text/event-stream",
            headers={
                "Content-Type": "text/event-stream",
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
            },
        )

    # Standard processing (no deep search)
    # Return streaming response
    # make it like a dict to store in mongo to avoid this error
    # bson.errors.InvalidDocument: cannot encode object: SelectionItem(category='Data', option='Market & Company Data', sub_option='Market Capitalization'), of type: <class 'app.schemas.chat_schemas.SelectionItem'>
    data.selections = [selection.model_dump() for selection in data.selections]
    # Convert chartData to dict format for MongoDB storage
    # data.chartData = [chart_item.model_dump() for chart_item in data.chartData] if data.chartData else []
    response = StreamingResponse(
        streaming_generic_chat_service(
            db,
            chat_id,
            data.account_id,
            data.trading_token,
            params,
            data.lens_id or "",
            generic_chat_history=generic_chat_history,
            is_websearch_enabled=data.is_websearch_enabled,
            question=user_question,
            reply=data.reply,
            token=data.token,
            track=track,
            selected_category=data.selected_category,
            selected_option=data.selected_option,
            background_tasks=background_tasks,
            parent=data.parent,
            edited_question=data.edited_question,
            original_question=data.question,
            selections=data.selections,
            file_paths=data.file_paths,
        ),
        media_type="text/event-stream",
        headers={
            "Content-Type": "text/event-stream",
            "Cache-Control": "no-store",
            "Connection": "keep-alive",
            "Transfer-Encoding": "chunked",
        },
    )
    return response  # Final response if no image-based response was found


async def create_fallback_drawings(data: ChartDrawingRequest) -> list:
    """Create fallback drawing objects when LLM parsing fails"""
    drawings = []

    # Calculate basic support and resistance levels
    prices = [candle.close for candle in data.chartData]
    highs = [candle.high for candle in data.chartData]
    lows = [candle.low for candle in data.chartData]

    # Find recent high and low
    recent_high = max(highs[-10:]) if len(highs) >= 10 else max(highs)
    recent_low = min(lows[-10:]) if len(lows) >= 10 else min(lows)

    # Create support rectangle
    support_drawing = {
        "id": str(uuid.uuid4()),
        "type": "rectangle",
        "topLeft": {"x": data.chartData[-5].datetime, "y": recent_low + 0.0001},
        "bottomRight": {"x": data.chartData[-1].datetime, "y": recent_low - 0.0001},
        "style": {
            "borderColor": "#089981B3",
            "backgroundColor": "#089981B3",
            "borderWidth": 1,
        },
    }

    # Create resistance rectangle
    resistance_drawing = {
        "id": str(uuid.uuid4()),
        "type": "rectangle",
        "topLeft": {"x": data.chartData[-5].datetime, "y": recent_high + 0.0001},
        "bottomRight": {"x": data.chartData[-1].datetime, "y": recent_high - 0.0001},
        "style": {
            "borderColor": "#FF6B6B",
            "backgroundColor": "#FF6B6B",
            "borderWidth": 1,
        },
    }

    drawings.extend([support_drawing, resistance_drawing])
    return drawings

import time

@router.post("/analyze-chart-drawings-v2")
# @ai_call_limit
async def analyze_chart_drawings_v2(request: Request, data: ChartDrawingRequest):
    try:
        #Put start and end time to log the processing time
        start_time = time.time()
        print("start time: ", start_time)
        response = client.responses.create(
            # model=settings.GPT_4O_MINI_MODEL_NAME,
            model=settings.gpt_4_1_mini,
            input=[
                {
                    "role": "system",
                    "content": [
                        {
                            "type": "input_text",
                            "text": """
                            1. Overview

1.1. Inputs
User Question: Natural-language request specifying patterns/levels to detect (e.g., “Identify support levels”, “Mark bullish engulfing patterns”, “Highlight consolidation zones”).
Tool Type (optional): rectangle, line, or annotation.
Chart Data: OHLCV time series with ISO 8601 timestamp and volume.
Time Range: start and end datetimes defining analysis window.
1.2. Objectives
Detect significant chart levels: support, resistance, consolidation zones, dynamic MA levels, psychological levels.
Identify chart patterns: triangles, wedges, flags, channels, head & shoulders, double/triple tops/bottoms, cup & handle, diamonds, broadening formations, etc.
Identify candlestick patterns: engulfing, doji variants, hammer/hanging man, stars, morning/evening star, three methods, abandoned baby, etc.
Identify price action phenomena: accumulation/distribution areas, breakouts, fakeouts, pullbacks, order blocks, divergence, parabolic moves, volatility spikes, ranges, consolidations, order flow events.
Produce JSON drawing objects (rectangle, line, annotation) with precise timestamps and price values.
Return only a JSON array of objects (or []). No explanatory text.
2. Data Precision and Formatting

2.1. Datetime
Use exact bar timestamp from OHLCV data in ISO 8601 format (e.g., 2025-06-06T09:15:00.000Z).
2.2. Price Levels
Use exact OHLC values, rounded to three decimals (e.g., 164.480).
2.3. Volume
Raw values for spike detection and averages.
2.4. Output
JSON array of drawing objects only.
3. Sequential Processing & Timestamp Anchoring

3.1. Chronological Scan
Process bars in ascending time order.
3.2. Detection Anchor
When a pattern or level is first detected at bar i, record its timestamp and relevant price (open/high/low/close).
3.3. Drawing Extension
Lines: start.x = first detection timestamp; end.x = last confirmation timestamp + N bars (default N=2).
Rectangles: topLeft.x = first zone bar timestamp; bottomRight.x = last zone bar timestamp + 1 bar.
Annotations: point.x = detection bar timestamp; point.y = bar’s close price.
3.4. Price Matching
At least 1 drawn y must equal one of the anchor bar’s OHLC values.
when drawing support and resistance levels, make sure that the bottomRight timedtamp is extended to at least 20 bars into the future from the topLeft timestamp.
4. Detection Methods

4.1. Support & Resistance
Pivot Low/High: For each bar i, check:
low[i] < min(low[i–N…i–1], low[i+1…i+N]) → pivot low
high[i] > max(high[i–N…i–1], high[i+1…i+N]) → pivot high
Clustering: Group pivot prices within ±0.1% using hierarchical clustering; set level = median of cluster.
Touch Count: Count bars where price falls/rises within tolerance of level and reverses direction; require ≥2 touches.
Dynamic SR: Compute moving average (e.g., MA50). Detect bounces where price touches MA within ATR tolerance.
Psychological: Identify round numbers (e.g., integer prices). Confirm with ≥2 touches and volume criterion.

4.2. Trendlines & Channels
Trendline via Pivots: Select two pivot lows/highs by ascending time; fit a line y = m·t + b. Extend from first to last pivot plus N bars.
Regression Line: On pivot coordinates (time→timestamp index, price→pivot price), compute least-squares m,b. Validate by checking residuals ≤ ATR threshold.
Parallel Channel: Offset regression/trendline by channel width equal to max deviation of opposite pivots. Confirm ≥2 touches on each boundary.
Validation: Ensure subsequent touches occur within ±ATR(i) tolerance; orientation classified by m > 0 (ascending), m < 0 (descending), m ≈ 0 (horizontal).
4.3. Chart Patterns
Triangles: Two highs at similar price ±tolerance and two ascending/descending lows. Compute lines; check convergence. Flag breakout on close > resistance line by >0.1% and volume spike.
Wedges: Two higher lows and two higher highs (rising wedge) with converging slope difference. Mark boundaries, confirm breakdown when price closes below support trendline by >ATR.
Flags/Pennants: After rapid move (flagpole), find small parallel (flag) or converging (pennant) patterns across K bars. Confirm continuation on exit with volume > average.
Head & Shoulders: Three peaks/troughs, middle (head) is extreme. Draw neckline; confirm reversal on break with volume.
Double/Triple Tops/Bottoms: 2–3 pivot highs/lows within tolerance. Neckline at intervening valley/peak; signal reversal on breakout with volume filter.
Cup & Handle: U-shape trough with equal highs; detect small pullback handle. Confirm breakout on close above rim by >0.1% with volume spike.
Diamonds & Broadening: Four pivots forming expanding then contracting pattern. Fit boundaries; signal pattern completion on break with volume.
Flag Variations & Rectangles: Small channels/ranges; confirm breakouts/breakdowns similarly.
4.4. Candlestick Patterns
4.4.1. Metrics

For each candle, compute:
body = |close–open|
upperShadow = high–max(open, close)
lowerShadow = min(open, close)–low
range = high–low
4.4.2. Thresholds

Use body-to-range ratio and shadow-to-body ratio per IG guide.
4.4.3. Single-Candle

Doji: body ≤ 0.1·range
Hammer/Hanging Man: lowerShadow ≥ 2·body, upperShadow ≤ 0.25·body, body in upper 25% of range
Inverted Hammer/Shooting Star: upperShadow ≥ 2·body, lowerShadow ≤ 0.25·body, body in lower 25% of range
Marubozu: body ≥ 0.9·range, shadows ≤ 0.1·range
4.4.4. Two-Candle

Engulfing: Candle2 body engulfs Candle1 body, opposite color
Piercing/Dark Cloud: Candle2 opens beyond Candle1 low/high and closes >50% into Candle1 body
Harami: Candle1 large body, Candle2 small body within Candle1 range; Cross if Candle2 is doji
Tweezers: Two candles share identical or near-identical highs (tops) or lows (bottoms)
4.4.5. Three-Candle & Multi

Morning/Evening Star: Three-candle pattern with small middle candle at gap and large third candle closing into first’s body ≥50%
Three White Soldiers/Black Crows: Three consecutive candles with bodies increasing and opens within previous bodies
Three Methods: Trend candles separated by 3–4 small opposite candles that stay within first candle’s range
Abandoned Baby: Gapped doji between two trend candles
4.4.6. Confirmation

Require Candleₙ₊₁ close beyond pattern extreme and volume[n+1] ≥ average(volume[n–M…n])
4.5. Price Action Concepts
Accumulation/Distribution: Sideways price over L bars, ATR decreasing below ATR_MA, volume contraction with occasional spikes; mark zone rectangle.
Breakout/Breakdown: If close[i] > SR_level + 0.001·price or < level – threshold and volume[i] ≥ avg, annotate event.
Fakeout: Brief breach of level then return within N bars; volume < 1.2×avg.
Pullback/Retest: After breakout, look for close back within tolerance of level on lower volume; confirm bounce with next bar.
Order Blocks: Identify last bearish/bullish candle before strong move (≥2·average body); mark preceding bar range.
Divergence: Compare price pivot direction vs. oscillator pivot (e.g., RSI) on same bars; annotate if mismatch.
Parabolic Moves: Compute returns acceleration + rising volume; annotate peak bar.
Volatility Zones: ATR spikes > k·ATR_avg; mark rectangle for duration.
Ranges/Consolidations: Horizontal channel via constant SR over T bars; ATR low; mark rectangle.
Order Flow Events: Bars where volume > k·avg and directional close; annotate as aggressive buy/sell bars.
5. JSON Drawing Structures

5.1. Rectangle
{
  "title": "<Zone Title>",
  "type": "rectangle",
  "topLeft": { "x": "<ISO timestamp>", "y": <price_upper> },
  "bottomRight": { "x": "<ISO timestamp>", "y": <price_lower> },
  "style": {
    "borderColor": "<hex>",
    "backgroundColor": "<hex+opacity>",
    "borderWidth": 1
  }
}
5.2. Line
{
  "title": "<Line Title>",
  "type": "line",
  "start": { "x": "<ISO timestamp>", "y": <price> },
  "end": { "x": "<ISO timestamp>", "y": <price> },
  "style": {
    "color": "<hex>",
    "width": 1,
    "lineType": "solid",
    "tooltip": "<string or undefined>"
  }
}
5.3. Annotation
{
  "title": "<Annotation Title>",
  "type": "annotation",
  "point": { "x": "<ISO timestamp>", "y": <price> }
}
5.4. Price Matching Rule: Each y must equal one of that bar’s OHLC values.
6. JSON Output Construction

6.1. Iterate detections: levels, patterns, candlesticks, events.
6.2. Build objects per Section 5.
6.3. Bundle into a single JSON array.
6.4. Return empty [] if none.
6.5. Response Wrapping: The final output must be a list of drawing objects only.
No additional text outside the JSON.
Example:

[
  {
    "title": "Support Zone at 165.030",
    "type": "rectangle",
    "topLeft": { "x": "2025-06-06T15:45:00.000Z", "y": 165.03 },
    "bottomRight": { "x": "2025-06-06T20:45:00.000Z", "y": 165.000 },
    "style": {
      "borderColor": "#00FF00",
      "backgroundColor": "#00FF0033",
      "borderWidth": 1
    }
  },
  {
    "title": "Resistance Zone at 164.900",
    "type": "rectangle",
    "topLeft": { "x": "2025-06-06T12:45:00.000Z", "y": 164.902 },
    "bottomRight": { "x": "2025-06-06T13:00:00.000Z", "y": 164.720 },
    "style": {
      "borderColor": "#FF0000",
      "backgroundColor": "#FF000033",
      "borderWidth": 1
    }
  }
]
7. Tool Selection Logic

7.1. If tool_type provided → use for all.
7.2. Else:
Discrete levels/trendlines → line.
Zones/ranges → rectangle.
Individual events/patterns → annotation.
8. Color Scheme

Bullish: Lines/annotations = #00FF00; rectangles = #00FF0033.
Bearish: Lines/annotations = #FF0000; rectangles = #FF000033.
9. Algorithmic Guidelines

Refer to Sections 4.x methods for pivot logic, clustering, ATR, regression, pattern boundaries, volume thresholds, gap detection.
10. Usage Workflow

Parse input.
Compute indicators (ATR, MAs, RSI, volume avg).
Detect pivots & cluster S/R.
Fit trendlines/channels.
Identify chart patterns.
Scan candlestick patterns.
Detect price action concepts.
Construct JSON drawing objects.
Return JSON array.
11. Implementation Considerations

11.1. Parameterize windows/tolerances by timeframe.
11.2. Skip divergence if no oscillator.
11.3. Clip patterns at range boundaries.
11.4. Maintain ISO timestamps & three‐decimal prices.
11.5. Validate JSON syntax.
12. Strict Trendline Construction

12.1. Anchor Selection
Gather an ordered list of swing points:
Uptrend swings = troughs (local minima)
Downtrend swings = peaks (local maxima)
Iterate through pairs (A, B) of swings in time order, requiring:
Uptrend: B.price > A.price
Downtrend: B.price < A.price
Compute line parameters:
slope = (B.price – A.price) / (B.time – A.time)
intercept = A.price – slope × A.time
Strict-connection test: For every intermediate swing C between A and B, verify:
Uptrend: C.price ≥ (slope × C.time + intercept)
Downtrend: C.price ≤ (slope × C.time + intercept)
The first valid (A, B) pair is your trendline anchors.
12.2. Drawing & Extending
Plot: TrendLine(t) = slope × t + intercept
Extend forward beyond B.time.
As new swings form, confirm none cross to invalidate the line—only A and B should ever touch.
13. Swing-Point Detection

13.1. Local Peaks & Troughs
A bar i is a peak if:
High[i] > max(High[i–k…i–1]) and High[i] > max(High[i+1…i+k])
A trough analogously with Low and min().
Parameter k (1–5 bars) controls noise vs. sensitivity.
13.2. Fractal Highs & Lows
Fractal high at i if:
High[i] > High[i–1], High[i–2] and High[i] > High[i+1], High[i+2]
Fractal lows defined with Low and “<”.
13.3. ZigZag Filter
Filters out moves smaller than threshold T.
State machine alternates “searching_high” / “searching_low,” marking a swing only when price moves ≥ T from the last extreme.
14. Basic Trendline Drawing

14.1. Identify Trend
Uptrend: higher highs & higher lows
Downtrend: lower lows & lower highs
14.2. Select Two Anchors
Uptrend: two swing lows L₁→L₂ with L₂ > L₁
Downtrend: two swing highs H₁→H₂ with H₂ < H₁
14.3. Draw & Extend
slope = (P₂ – P₁) / (t₂ – t₁)
intercept = P₁ – slope × t₁
TrendLine(t) = slope·t + intercept for t ≥ t₂
Validate: look for ≥3 touches (excluding the anchors) to confirm strength.
15. Consecutive Strong Moves

X = 0.5% (minimum single-bar gain)
K = 3 (number of consecutive bars)
streak = 0
For each bar i:
move = (Close[i] – Open[i]) / Open[i]
if move ≥ X: streak += 1; if streak == K, record “bullish streak” ending at i
else: streak = 0
Bearish variant flips the inequality.
16. N-Day Highs / Lows

N = 20
For i = N … end:
if High[i] ≥ max(High[i–N … i–1]): record new N-day high at i
if Low[i] ≤ min(Low[i–N … i–1]): record new N-day low at i
Combine with volume filters to confirm breakouts.
17. Average True Range (ATR)

TR[i] = max(High[i] – Low[i], abs(High[i] – Close[i–1]), abs(Low[i] – Close[i–1]))
ATR[i] = EMA(TR, length=14)
Application: set stops at 1–3× ATR or scale position size inversely to ATR.
18. Pivot Points (Daily)

P = (High_prev + Low_prev + Close_prev) / 3
R1 = 2·P – Low_prev
S1 = 2·P – High_prev
R2 = P + (High_prev – Low_prev)
S2 = P – (High_prev – Low_prev)
19. Moving-Average Crossovers

fast = EMA(Close, L1)
slow = EMA(Close, L2) (L2 > L1)
if fast[i] > slow[i] and fast[i–1] ≤ slow[i–1]: bullish crossover
if fast[i] < slow[i] and fast[i–1] ≥ slow[i–1]: bearish crossover
20. RSI Overbought / Oversold

Compute average gains/losses via EMA (period = 14)
RS = (Avg up) / (Avg down)
RSI = 100 – (100 / (1 + RS))
Signals: RSI > 70 → overbought; RSI < 30 → oversold
21. Volume Spike Detection

M = 2; length = 20
For i = length … end:
avg_vol = SMA(Volume[i–length … i–1])
if Volume[i] ≥ M * avg_vol: mark volume spike at i
22. Candlestick Pattern Recognition

See Section 4.4.3–4.4.5 for formal rules:
Engulfing: current body fully contains previous bar’s body in the opposite direction.
Hammer / Shooting Star: small real body near one end, long tail ≥ 2× body.
Doji: open ≈ close (indecision).
Implement as Boolean rules on O/H/L/C for each pattern.
23. Support & Resistance Zone Identification

23.1. Price Clustering
Bin swing highs/lows into price intervals of width ΔP (e.g. 0.5% of current price).
23.2. Touch-Count Threshold
Any bin with ≥ 2 swing touches becomes a marked support/resistance zone.


24. System Prompt Guide

"You are a highly skilled chart technical analyst. When given a user query and OHLCV data, proceed in two steps:
Pattern Detection: Scan the data chronologically to identify all relevant chart patterns, support and resistance levels, candlestick formations, and price action events, recording each detection with its timestamp, price, and type.
Drawing Construction: Convert your findings into JSON drawing objects (lines, rectangles, annotations) using exact ISO timestamps, three‐decimal price values, and the defined color scheme. Return only a JSON object with no additional text."
25. Label Instructions
Never add the date to the label. If the label has many characters, shorten it but providing the starting capitals, for example: "Support Zone at 165.030" should be "Support Zone" or "Tweezer Pattern" should be "Tweezer" 



26. System Prompt Guide

"You are a highly skilled chart technical analyst. When given a user query and OHLCV data, proceed in two steps:
Pattern Detection: Scan the data chronologically to identify all relevant chart patterns, support and resistance levels, candlestick formations, and price action events, recording each detection with its timestamp, price, and type.
Drawing Construction: Convert your findings into JSON drawing objects (lines, rectangles, annotations) using exact ISO timestamps, four‐decimal price values, and the defined color scheme. Return only a JSON object with no additional text."
IMPORTANT:
The timestamps in json drawing tools should ALWAYS match the OHLCV data timestamps
Be very strict with finding the patterns, dont be lazy, and dont miss any patterns, try to find ALL the patterns.Think algorithmically.
Also be open to combinations of patterns, for example a doji pattern below 50 ema. This means that you have to calculate the 50 ema and then check if the doji is below it.




                           """,
                        }
                    ],
                },
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "input_text",
                            "text": f"Here is the user's question: {data.question}\n and its tool type is {data.tool_type}\n and here is the Chart Data:: {data.chartData}",
                        }
                    ],
                },
            ],
            text={"format": {"type": "text"}},
            temperature=1,
            max_output_tokens=2048,
            top_p=1,
        )

        result = response.output_text
        result = result.replace("```json", "").replace("```", "").strip()
        end_time = time.time()
        print(f"Time taken: {end_time - start_time} seconds")

        # Parse the JSON string response into a proper JSON object
        try:
            import json
            parsed_result = json.loads(result)
            # Return the parsed result wrapped in a drawings object
            return {"drawings": parsed_result}
        except json.JSONDecodeError as json_error:
            print(f"JSON parsing error: {json_error}")
            print(f"Raw result: {result}")
            # Return an empty drawings array as fallback with proper JSON structure
            return {"drawings": []}
    except Exception as e:
        print(f"error coming: {e}")
        return {"error": str(e)}


# This is with indicator endpoint call
@router.post("/analyze-chart-drawings-v3")
@ai_call_limit
async def analyze_chart_drawings_v3(request: Request, data: drawing_tool):
    # Load a semantic model
    model = SentenceTransformer("all-MiniLM-L6-v2")  # Fast and good enough

    # Create corpus of indicator texts
    corpus = [f"{ind['title']} - {ind['description']}" for ind in indicators]
    corpus_embeddings = model.encode(corpus, convert_to_tensor=True)

    # Define a function to query
    def search_indicators(query, top_k=3):
        query_embedding = model.encode(query, convert_to_tensor=True)
        hits = util.semantic_search(query_embedding, corpus_embeddings, top_k=top_k)[0]

        results = []
        for hit in hits:
            index = hit["corpus_id"]
            score = hit["score"]
            matched = indicators[index]
            results.append(
                {
                    "id": matched["id"],
                    "title": matched["title"],
                    "identifier": matched["identifier"],
                    "score": round(score, 1),
                }
            )
        return results

    return search_indicators(data.question)
