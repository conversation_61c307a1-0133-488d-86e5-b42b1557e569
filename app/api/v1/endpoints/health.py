"""
Health Check and Error Monitoring Endpoints

This module provides endpoints for system health monitoring and error metrics.
"""

import asyncio
from datetime import datetime
from typing import Dict, Any

from fastapi import APIRouter, Depends, HTTPException, Request
from sqlalchemy.ext.asyncio import AsyncSession

from app.db.database import get_db
from app.utils.error_handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, StandardizedError
from app.utils.error_monitoring import (
    error_monitor,
    get_error_metrics,
    get_health_status,
    monitor_error,
)
from app.utils.logger import chatbot_logger as logger

router = APIRouter()


@router.get("/health")
async def health_check():
    """
    Basic health check endpoint
    
    Returns:
        Health status information
    """
    try:
        return {
            "status": "healthy",
            "timestamp": datetime.utcnow().isoformat(),
            "service": "microservice-chat-backend",
            "version": "1.0.0"
        }
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(status_code=503, detail="Service unavailable")


@router.get("/health/detailed")
async def detailed_health_check(db: AsyncSession = Depends(get_db)):
    """
    Detailed health check with system components
    
    Returns:
        Comprehensive health status including database, error metrics, etc.
    """
    health_status = {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "service": "microservice-chat-backend",
        "version": "1.0.0",
        "components": {}
    }
    
    overall_healthy = True
    
    # Check database connectivity
    try:
        await db.execute("SELECT 1")
        health_status["components"]["database"] = {
            "status": "healthy",
            "message": "Database connection successful"
        }
    except Exception as e:
        logger.error(f"Database health check failed: {e}")
        health_status["components"]["database"] = {
            "status": "unhealthy",
            "message": f"Database connection failed: {str(e)}"
        }
        overall_healthy = False
    
    # Check error monitoring system
    try:
        error_health = get_health_status()
        health_status["components"]["error_monitoring"] = {
            "status": error_health["status"],
            "error_rate": error_health["error_rate"],
            "total_errors": error_health["total_errors"]
        }
        
        if error_health["status"] in ["critical", "warning"]:
            overall_healthy = False
    except Exception as e:
        logger.error(f"Error monitoring health check failed: {e}")
        health_status["components"]["error_monitoring"] = {
            "status": "unhealthy",
            "message": f"Error monitoring check failed: {str(e)}"
        }
        overall_healthy = False
    
    # Check external services (simulate)
    external_services = ["openai", "perplexity", "s3"]
    for service in external_services:
        try:
            # Simulate service check (in real implementation, you'd ping the service)
            await asyncio.sleep(0.01)  # Simulate network call
            health_status["components"][service] = {
                "status": "healthy",
                "message": f"{service} service accessible"
            }
        except Exception as e:
            logger.error(f"{service} health check failed: {e}")
            health_status["components"][service] = {
                "status": "unhealthy",
                "message": f"{service} service check failed: {str(e)}"
            }
            overall_healthy = False
    
    # Set overall status
    health_status["status"] = "healthy" if overall_healthy else "unhealthy"
    
    if not overall_healthy:
        raise HTTPException(status_code=503, detail=health_status)
    
    return health_status


@router.get("/health/errors")
async def error_metrics():
    """
    Get current error metrics and monitoring information
    
    Returns:
        Error metrics and system health based on error patterns
    """
    try:
        metrics = get_error_metrics()
        health = get_health_status()
        
        return {
            "timestamp": datetime.utcnow().isoformat(),
            "health_status": health["status"],
            "metrics": metrics,
            "monitoring": {
                "is_active": error_monitor.is_monitoring,
                "window_minutes": error_monitor.metrics.window_minutes
            }
        }
    except Exception as e:
        logger.error(f"Error metrics retrieval failed: {e}")
        raise HTTPException(
            status_code=500,
            detail="Failed to retrieve error metrics"
        )


@router.post("/health/test-error")
async def test_error_monitoring(request: Request):
    """
    Test endpoint to generate errors for monitoring system testing
    
    This endpoint is for testing purposes only and should be removed in production.
    """
    try:
        # Generate different types of test errors
        error_type = request.query_params.get("type", "general")
        
        if error_type == "validation":
            from app.utils.error_handler import ValidationError
            error = ValidationError(
                message="Test validation error",
                field="test_field"
            )
        elif error_type == "database":
            from app.utils.error_handler import DatabaseError
            error = DatabaseError(
                message="Test database error",
                operation="test_operation"
            )
        elif error_type == "external_api":
            from app.utils.error_handler import ExternalAPIError
            error = ExternalAPIError(
                service="test_service",
                message="Test external API error",
                status_code=500
            )
        elif error_type == "critical":
            from app.utils.error_handler import ErrorCode, ErrorCategory, ErrorSeverity
            error = StandardizedError(
                code=ErrorCode.INTERNAL_SERVER_ERROR,
                message="Test critical error",
                category=ErrorCategory.SYSTEM,
                severity=ErrorSeverity.CRITICAL
            )
        else:
            from app.utils.error_handler import ErrorCode, ErrorCategory, ErrorSeverity
            error = StandardizedError(
                code=ErrorCode.INTERNAL_SERVER_ERROR,
                message="Test general error",
                category=ErrorCategory.SYSTEM,
                severity=ErrorSeverity.MEDIUM
            )
        
        # Record the error in monitoring system
        monitor_error(error, {"test": True, "endpoint": "/health/test-error"})
        
        # Return error response
        return ErrorHandler.create_error_response(error, request)
    
    except Exception as e:
        logger.error(f"Test error generation failed: {e}")
        raise HTTPException(
            status_code=500,
            detail="Failed to generate test error"
        )


@router.get("/health/readiness")
async def readiness_check(db: AsyncSession = Depends(get_db)):
    """
    Kubernetes readiness probe endpoint
    
    Returns:
        Simple status for readiness checks
    """
    try:
        # Check critical dependencies
        await db.execute("SELECT 1")
        
        return {"status": "ready"}
    except Exception as e:
        logger.error(f"Readiness check failed: {e}")
        raise HTTPException(status_code=503, detail="Not ready")


@router.get("/health/liveness")
async def liveness_check():
    """
    Kubernetes liveness probe endpoint
    
    Returns:
        Simple status for liveness checks
    """
    try:
        # Basic application liveness check
        return {"status": "alive"}
    except Exception as e:
        logger.error(f"Liveness check failed: {e}")
        raise HTTPException(status_code=503, detail="Not alive")


@router.get("/health/metrics/summary")
async def metrics_summary():
    """
    Get a summary of key system metrics
    
    Returns:
        Summary of important system metrics
    """
    try:
        error_metrics = get_error_metrics()
        health_status = get_health_status()
        
        return {
            "timestamp": datetime.utcnow().isoformat(),
            "summary": {
                "overall_health": health_status["status"],
                "error_rate": health_status["error_rate"],
                "total_errors": error_metrics["total_errors"],
                "top_errors": error_metrics["top_errors"][:3],  # Top 3 errors
                "monitoring_active": error_monitor.is_monitoring
            },
            "alerts": {
                "critical_errors": error_metrics["error_counts"].get("severity_critical", 0),
                "high_errors": error_metrics["error_counts"].get("severity_high", 0),
                "api_failures": error_metrics["error_counts"].get("category_external_api", 0)
            }
        }
    except Exception as e:
        logger.error(f"Metrics summary failed: {e}")
        raise HTTPException(
            status_code=500,
            detail="Failed to retrieve metrics summary"
        )
