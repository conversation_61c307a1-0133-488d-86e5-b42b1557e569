from fastapi import APIRouter

from app.api.v1.endpoints.agents import router as agents_router
from app.api.v1.endpoints.chats import router as chats_router
from app.api.v1.endpoints.folders import router as folders_router
from app.api.v1.endpoints.general import router as general_router
from app.api.v1.endpoints.generic_bot import router as generic_router
from app.api.v1.endpoints.lookalike import router as lookalike_router
from app.api.v1.endpoints.prompts import router as prompts_router
from app.api.v1.endpoints.sanity_prompt import router as sanity_prompt_router
from app.api.v1.endpoints.vuevat import router as vuevat_router
from app.api.v1.endpoints.daily_summary import router as daily_summaries_router

api_v1 = APIRouter()

api_v1.include_router(agents_router, prefix="/agents", tags=["agents"])
api_v1.include_router(folders_router, prefix="/folders", tags=["folders"])
api_v1.include_router(chats_router, prefix="/chats", tags=["chats"])
api_v1.include_router(general_router, prefix="/general", tags=["general"])
api_v1.include_router(generic_router, prefix="/generic_bot", tags=["generic_bot"])
api_v1.include_router(lookalike_router, prefix="/lookalike", tags=["lookalike"])
api_v1.include_router(sanity_prompt_router, prefix="/sanity", tags=["prompts"])
api_v1.include_router(prompts_router, prefix="/prompts", tags=["prompts"])
api_v1.include_router(vuevat_router, prefix="/vuevat", tags=["vuevat"])
api_v1.include_router(daily_summaries_router, prefix="/summaries", tags=["daily_summaries"])
