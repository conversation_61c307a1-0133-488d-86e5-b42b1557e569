import re
import time
import uuid

from fastapi import APIRouter, Depends, HTTPException, Request
from fastapi_pagination import Params, add_pagination
from openai import OpenAI
from sqlalchemy.ext.asyncio import AsyncSession
from fastapi.responses import StreamingResponse

from app.core.config import settings
from app.crud.chat_crud import create_chat, create_conversation
from app.db.database import get_db
from app.schemas.drawing_tool_schemas import AnalysisResponse, AnalysisRequest, DrawingToolChatSchema
from app.services.generic_chat_service import streaming_drawing_chat_service_v3, generate_drawing_instuaction
from app.utils import cache
from app.utils.drawing_tool_utils import get_antropi_openai_client, TechnicalAnalysisAgent
from app.utils.general_utils import generate_chat_name
from app.utils.logger import generic_logger as logger

router = APIRouter()
add_pagination(router)


async def create_root_chat(
    db: AsyncSession, account_id: str, question="", chat_type="DRAWING"
):
    chat_id = str(uuid.uuid4())
    parent = chat_id
    await create_conversation(
        db,
        chat_id=chat_id,
        account_id=account_id,
        chat_type=chat_type,
        name=generate_chat_name(question),
    )
    await create_chat(chat_data={"id": chat_id, "chat_id": chat_id, "parent": parent})
    return chat_id, parent

@router.post("/get_drawing_tool_data", response_model=AnalysisResponse)
async def get_drawing_tool_data(
        request: Request,
        data: AnalysisRequest
    ):
    """
    Perform technical analysis with provided metadata

    Example request:
    {
        "query": "Show RSI levels with order blocks",
        "metadata": {
            "symbol": "EURUSD",
            "timeframe": "15m",
            "ohlc_data": [
                {
                    "datetime": "2025-06-30T21:10:00.000Z",
                    "open": 1.17798,
                    "close": 1.1771,
                    "high": 1.1782,
                    "low": 1.17707,
                    "volume": 36
                }
            ]
        }
    }
    """
    customer_id = request.state.customer_id
    track = f"{customer_id}_{data.account_id or 0}"
    start_time = time.time()


    try:
        logger.info("=" * 50)
        logger.info(f"🚀 STARTING ANALYSIS")
        logger.info(f"📝 Query: '{data.query}'")
        logger.info(f"📊 Symbol: {data.metadata.get('symbol', 'N/A')}")
        logger.info(f"⏰ Timeframe: {data.metadata.get('timeframe', 'N/A')}")
        logger.info(f"📈 Data points: {len(data.metadata.get('ohlc_data', []))}")
        logger.info("=" * 50)

        # Validate metadata
        if not data.metadata.get("ohlc_data"):
            raise HTTPException(status_code=400, detail="OHLC data is required in metadata")

        # Initialize Analysis Agent
        client = get_antropi_openai_client()
        agent = TechnicalAnalysisAgent(client)

        # Step 1: Analyze query for indicators
        indicators = agent.analyze_query(data.query)
        if not indicators:
            raise HTTPException(status_code=400, detail="Could not identify indicators from query")

        # Step 2: Load calculation knowledge
        knowledge = agent.load_calculation_knowledge(indicators)

        # Step 3: Perform analysis
        analysis_result = agent.perform_analysis(data.query, data.metadata, indicators, knowledge)

        # Calculate processing time
        processing_time = time.time() - start_time

        # Prepare response metadata
        response_metadata = {
            "symbol": data.metadata.get("symbol", ""),
            "timeframe": data.metadata.get("timeframe", ""),
            "ohlc_data": data.metadata.get("ohlc_data", []),
        }
        drawing_id = str(uuid.uuid4())
        mapping_dict = {
            "drawing_id": drawing_id,
            "indicators_used": indicators,
            "explanation": analysis_result.get("explanation", ""),
            "drawing_instructions": analysis_result.get("drawing_instructions", {}),
            "metadata": response_metadata
        }
        cache.set_user_cache(f"context_drawing_tool_{track}_{drawing_id}", mapping_dict)

        logger.info(f"✅ Analysis completed in {processing_time:.2f} seconds")

        return AnalysisResponse(
            success=True,
            query=data.query,
            drawing_id=drawing_id,
            indicators_used=indicators,
            explanation=analysis_result.get("explanation", ""),
            drawing_instructions=analysis_result.get("drawing_instructions", {}),
            metadata=response_metadata,
            processing_time=processing_time
        )

    except HTTPException:
        raise
    except Exception as e:
        processing_time = time.time() - start_time
        logger.error(f"❌ Error in analysis: {str(e)}")

        return AnalysisResponse(
            success=False,
            query=data.query,
            drawing_id=str(uuid.uuid4()),
            indicators_used=[],
            explanation="",
            drawing_instructions={},
            metadata={},
            processing_time=processing_time,
        )

@router.post("/drawing-v3")
async def drawing(
    request: Request,
    data: DrawingToolChatSchema,
    db: AsyncSession = Depends(get_db),
):
    """
    Handles generic chat requests with a question, reply (optional), and account details.
    Validates inputs, handles chat branching logic, and constructs a user question.
    Retrieves chat history and checks if the request is an image-based request.
    If it is, calls the image chat service. Otherwise, calls the generic chat service.
    Returns the response from the service.

    Supports both single selection and multiple selections for comparative analysis.
    """
    customer_id = request.state.customer_id
    chat_id = data.chat_id
    track = f"{customer_id}_{data.account_id or 0}"
    context_chat_key = f"context_chat_key_{track}"
    context_chat = cache.get(context_chat_key) or {}
    if context_chat:
        data.chat_id = context_chat.get("chat_id")
        chat_id = data.chat_id
        data.parent = context_chat.get("parent")
        cache.delete(context_chat_key)
    logger.debug(f"Initializing chat service for customer: {customer_id}")
    if not data.chat_id:
        chat_id, parent = await create_root_chat(
            db, account_id=track, question=data.question
        )
        data.chat_id = chat_id
        data.parent = parent

    # Validate inputs early
    if not data.question:
        raise HTTPException(status_code=400, detail="No question provided")
    if not chat_id:
        raise HTTPException(status_code=400, detail="No chat_id provided")

    # Retrieve chat history
    history_key = f"chat_history_{chat_id}"
    generic_chat_history = cache.get(history_key) or []
    cache.set(history_key, generic_chat_history)

    # Process with generic chat service
    logger.info(f"Going into drawing chat service: {chat_id}")

    keywords = ["draw", "generate", "find", "pattern"]
    pattern = r'\b(?:' + '|'.join(keywords) + r')\b'
    if re.search(pattern, data.question.lower(), re.IGNORECASE) or "give" in data.question.lower() and "@" in data.question.lower():
        response = StreamingResponse(
            generate_drawing_instuaction(
                db,
                chat_id,
                data.account_id,
                data.drawing_id or "",
                data,
                generic_chat_history=generic_chat_history,
                question=data.question,
                track=track,
                parent=data.parent,
                original_question=data.question,
            ),
            media_type="text/event-stream",
            headers={
                "Content-Type": "text/event-stream",
                "Cache-Control": "no-store",
                "Connection": "keep-alive",
                "Transfer-Encoding": "chunked",
            },
        )
        return response
    else:
        response = StreamingResponse(
            streaming_drawing_chat_service_v3(
                db,
                chat_id,
                data.account_id,
                data.drawing_id or "",
                data,
                generic_chat_history=generic_chat_history,
                question=data.question,
                track=track,
                parent=data.parent,
                original_question=data.question,
            ),
            media_type="text/event-stream",
            headers={
                "Content-Type": "text/event-stream",
                "Cache-Control": "no-store",
                "Connection": "keep-alive",
                "Transfer-Encoding": "chunked",
            },
        )
        return response  # Final response if no image-based response was found

