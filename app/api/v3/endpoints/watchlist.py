from fastapi import APIRouter, Depends, HTTPException, Request
from fastapi.responses import JSONResponse
from sqlalchemy.ext.asyncio import AsyncSession

from app.db.database import get_db
from app.utils.watchlist_utils import getting_watchlist_data_for_ticker

router = APIRouter()

@router.get("/watchlist_symbol_v3")
async def watchlist_symbol(
    request: Request,
    ticker: str,
    account_id = "",
    db: AsyncSession = Depends(get_db),
):
    print("request coming")
    if account_id:
        account_id = f"{request.state.customer_id}_{account_id}"
    else:
        account_id = f"{request.state.customer_id}_0"

    response = await getting_watchlist_data_for_ticker(
        db=db,
        ticker=ticker
    )

    return JSONResponse(response)
