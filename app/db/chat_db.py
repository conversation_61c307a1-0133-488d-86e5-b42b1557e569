import motor.motor_asyncio
from pymongo import ASCENDING

from app.core.config import settings


class MongoDB:
    def __init__(self, db_name: str, collection_name: str):
        self.client = motor.motor_asyncio.AsyncIOMotorClient(
            settings.MONGO_URL.replace(
                r"{ca_file_path}", "/home/<USER>/chatbot-service/global-bundle.pem"
            )
        )
        self.db = self.client[db_name]
        self.collection = self.db[collection_name]

    async def create_indexes(self):
        await self.collection.create_index([("chat_id", ASCENDING)], unique=True)

    def get_collection(self):
        return self.collection


async def get_chat_collection():
    mongo_db = MongoDB(db_name="chat_db", collection_name="chat_records")
    await mongo_db.create_indexes()
    return mongo_db.get_collection()


async def delete_collection():
    mongo_db = MongoDB(db_name="chat_db", collection_name="chat_records")
    await mongo_db.collection.delete_many({})


async def get_collection():
    mongo_db = MongoDB(db_name="chat", collection_name="chat_data")
    return mongo_db.get_collection()
