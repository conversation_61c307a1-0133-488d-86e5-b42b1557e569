import traceback

import pymysql
from fastapi import HTTPException
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker
from tenacity import retry, stop_after_attempt, wait_fixed

from app.core.config import settings
from app.services.mail_service import send_test_mail
from app.utils.db_error_handler import DatabaseErrorHandler, handle_database_errors
from app.utils.error_handler import <PERSON><PERSON>r<PERSON><PERSON><PERSON>
from app.utils.logger import chatbot_logger as logger


# DB engine creator with robust pool config
def get_engine(database_url: str):
    return create_async_engine(
        database_url,
        echo=True,
        pool_size=10,
        max_overflow=20,
        pool_timeout=30,
        pool_recycle=1800,
        connect_args={"connect_timeout": 10},
    )


# Create main DB engine and session
try:
    engine = get_engine(settings.DATABASE_URL)
    async_session = sessionmaker(engine, class_=AsyncSession, expire_on_commit=False)
except Exception:
    logger.critical(f"Database Connection Error: {traceback.format_exc()}")
    engine = None
    async_session = None


# Retry wrapper for DB operations
@retry(stop=stop_after_attempt(3), wait=wait_fixed(2))
async def safe_query(session, query_func):
    return await query_func(session)


# Dependency for main DB session
@handle_database_errors("main_db_session")
async def get_db():
    if async_session is None:
        logger.error("Database session unavailable.")
        db_error = DatabaseErrorHandler.classify_database_error(
            Exception("Database session unavailable")
        )
        raise ErrorHandler.create_error_response(db_error)

    session = async_session()
    try:
        yield session
    except Exception as e:
        logger.error(f"Main DB Query Error: {traceback.format_exc()}")
        send_test_mail(f"{traceback.format_exc()} \n {settings.DATABASE_URL}")

        # Use standardized error handling
        db_error = DatabaseErrorHandler.classify_database_error(e)
        raise db_error
    finally:
        await session.close()


# Create trade DB engine and session
try:
    trade_engine = get_engine(settings.TRADES_DATABASE)
    async_trade_session = sessionmaker(
        trade_engine, class_=AsyncSession, expire_on_commit=False
    )
except Exception:
    logger.critical(f"Trade DB Connection Error: {traceback.format_exc()}")
    trade_engine = None
    async_trade_session = None


# Dependency for trade DB session
@handle_database_errors("trade_db_session")
async def get_trade_db():
    if async_trade_session is None:
        logger.error("Trade DB session unavailable.")
        db_error = DatabaseErrorHandler.classify_database_error(
            Exception("Trade DB session unavailable")
        )
        raise db_error

    session = async_trade_session()
    try:
        yield session
    except Exception as e:
        logger.error(f"Trade DB Query Error: {traceback.format_exc()}")

        # Use standardized error handling
        db_error = DatabaseErrorHandler.classify_database_error(e)
        raise db_error
    finally:
        await session.close()
