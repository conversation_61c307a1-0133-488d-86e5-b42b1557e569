from tiktoken import encoding_for_model

from app.core.config import settings


def calculate_tokens_and_cost(
    messages, response_tokens, model_name=settings.MODEL_NAME
):
    """Calculate token counts and costs for the API call."""
    # Get the appropriate tokenizer
    if settings.MODEL_NAME in model_name.lower():
        enc = encoding_for_model(settings.MODEL_NAME)
    else:
        enc = encoding_for_model(settings.GPT_4O_MINI_MODEL_NAME)

    # Count input tokens
    input_tokens = 0
    for msg in messages:
        input_tokens += len(enc.encode(msg["content"]))
        input_tokens += (
            4  # Every message follows <im_start>{role/name}\n{content}<im_end>\n
        )

    # Count output tokens
    output_tokens = response_tokens

    # Calculate costs based on model (prices are per 1M tokens)
    if model_name == settings.MODEL_NAME or model_name == settings.MODEL_NAME:
        input_cost = (input_tokens / 1000000) * 2.50
        output_cost = (output_tokens / 1000000) * 10.00
    elif (
        model_name == settings.GPT_4O_MINI_MODEL_NAME
        or model_name == settings.GPT_4O_MINI_MODEL_NAME
    ):
        input_cost = (input_tokens / 1000000) * 0.15
        output_cost = (output_tokens / 1000000) * 0.60
    else:
        input_cost = 0
        output_cost = 0

    return {
        "input_tokens": input_tokens,
        "output_tokens": output_tokens,
        "input_cost": "{:.6f}".format(input_cost),
        "output_cost": "{:.6f}".format(output_cost),
        "total_cost": "{:.6f}".format(input_cost + output_cost),
    }
