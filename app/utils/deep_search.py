import asyncio
import json
import logging
import re
from concurrent.futures import <PERSON>hr<PERSON><PERSON><PERSON><PERSON>xecutor, as_completed
from datetime import datetime
from typing import Any, Dict, List

from langchain.schema import HumanMessage, SystemMessage
from langchain.schema.output_parser import StrOutputParser
from langchain.schema.runnable import RunnablePassthrough
from langchain_community.chat_models import ChatPerplexity
from langchain_openai import ChatOpenAI
from openai import OpenAI

from app.core.config import settings
from app.core.llm_config import client
from app.utils.comprehensive_cost_tracker import cost_tracker, track_openai_response

logger = logging.getLogger(__name__)

date_context = f"Today's date is {datetime.today():%Y-%m-%d}"
current_date = datetime.today()

# Initialize Perplexity client for Sonar model using existing config
perplexity_client = OpenAI(
    api_key=settings.PERPLEXITY_KEY, base_url="https://api.perplexity.ai"
)

# System prompt for streaming responses
system_prompt_stream = f"""You are Vuetra's Financial/Stock and News AI Expert.
Use deep reasoning.

if date is mentioned or current date, time, latest, recent, etc. is mentioned in the question, then consider the information as of {date_context}, otherwise do it according to the questions

For the given question, provide:
1. A direct, factual answer based on web search results
2. Any relevant data points or statistics
3. Context to understand the answer

Start with Analysis title what you are analysing for that question and end with "Conclusion:".
If info is time-sensitive with current date, cite as of {current_date.strftime('%B %d, %Y')}, otheriwise no need to mention the date.

Important Note:-
- Respond only in html rich text with tags like <strong>, <p>, <ul>, <li> etc. This should be directly rendered in the UI with innerHTML."

Response Format must have:
- Use <h2> for main headings
- Use <h3> for subsections
- Use <p> for paragraphs
- Use <ul> and <li> for lists
- Use <strong> for emphasis
- Use <div class="deep-search-result"> as the outer container

"""


async def deep_search(
    question: str, history: List[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    Performs a deep search using a chain of thought approach to find more comprehensive answers.

    Args:
        question: The user's question
        history: Chat history for context

    Returns:
        Dict containing the search results and reasoning
    """
    logger.info(f"Starting deep search for question: {question}")

    # Step 1: Initial analysis and sub-question generation
    analysis, sub_questions = await analyze_and_generate_sub_questions(
        question, history
    )
    logger.info(f"Generated {len(sub_questions)} sub-questions")

    # Step 2: Research all sub-questions in parallel
    research_results = await research_sub_questions_parallel(sub_questions)

    # Step 3: Synthesize the findings
    synthesis = await synthesize_findings(question, research_results)

    logger.info("Deep search completed")

    return {
        "answer": synthesis,
        "analysis": analysis,
        "sub_questions": sub_questions,
        "research_results": research_results,
    }


async def analyze_and_generate_sub_questions(
    question: str, history: List[Dict[str, Any]] = None
) -> tuple:
    """
    Combined function to analyze question and generate sub-questions.

    Args:
        question: The main question to analyze
        history: Chat history for context

    Returns:
        Tuple of (analysis, sub_questions)
    """
    # Prepare context from history if available
    context = ""
    if history:
        context = "Previous conversation:\n"
        for item in history[-3:]:  # Use last 3 exchanges for context
            if "question" in item and "answer" in item:
                context += f"User: {item['question']}\nAssistant: {item['answer']}\n\n"

    # Step 1: analysis + sub-question generation
    logger.info(f"Analyzing question: {question}")
    system_prompt = f"""You are Vuetra's Financial/Stock and News AI Expert.

            TASK 1: First, use reasoning to analyze this question:
            "{question}"

            {context}

            Provide a detailed analysis of:
            1. The core information being requested
            2. The domain knowledge required (finance, economics, trading, etc.)
            3. Any implicit assumptions in the question
            4. Any potential ambiguities that need clarification
            5. The type of data that would be most relevant (historical prices, company fundamentals, economic indicators, etc.)

            Start with Analysis title and walk through your reasoning process.
            End with a "Conclusion:" section that summarizes the key points.

            IMPORTANT: Format your summary response in HTML that can be directly rendered in a UI.
            - Respond only in html rich text with tags like <strong>, <p>, <ul>, <li> etc. This should be directly rendered in the UI with innerHTML."
            Use appropriate HTML tags like:
            - <h2> for section headings
            - <p> for paragraphs
            - <ul> and <li> for lists
            - <strong> for emphasis
            - <div> with appropriate classes for different sections

            TASK 2: Based on your analysis, generate 3-5 specific sub-questions that would help gather all the information needed for a comprehensive answer, this should be ouside the inner html, is should be in plan text format.

            Format your sub-questions as a JSON array at the end of your response in plain text, like this:
            SUB_QUESTIONS: ["Question 1?", "Question 2?", "Question 3?"]
            """

    try:
        # Try using the responses.create method with high reasoning
        enhanced_analysis_response = client.responses.create(
            model=settings.GPT_4O_MINI_MODEL_NAME,
            input=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": question},
            ],
        )

        full_text = enhanced_analysis_response.output_text
        logger.info("Successfully analyzed question with chain of thought reasoning")

    except Exception as e:
        # Fallback to standard completions API if responses API fails
        logger.warning(
            f"Error using responses.create API: {str(e)}. Falling back to standard completions API."
        )
        raise e

        # response = client.chat.completions.create(
        #     model=settings.GPT_4O_MINI_MODEL_NAME,
        #     messages=[
        #         {"role": "system", "content": system_prompt},
        #         {"role": "user", "content": question}
        #     ],
        #     temperature=0.2,
        #     max_tokens=1000
        # )
        # full_text = response.choices[0].message.content

    # Extract analysis (everything before SUB_QUESTIONS)
    sub_q_match = re.search(
        r"(?i)(?:SUB_QUESTIONS|QUESTIONS)\s*[:\-]\s*(\[.*\])",
        full_text,
        flags=re.DOTALL,
    )

    if sub_q_match:
        analysis = full_text[: sub_q_match.start()].strip()
        try:
            sub_questions = json.loads(sub_q_match.group(1))
        except json.JSONDecodeError:
            sub_questions = re.findall(r'"([^"]*\?)"', sub_q_match.group(1))
    else:
        # Fallback parsing
        analysis = full_text
        sub_questions = re.findall(r"\d+\.\s*(.*?\?)", full_text)
        if not sub_questions:
            sub_questions = re.findall(
                r"^(?:-|\*)\s*(.+\?)$", full_text, flags=re.MULTILINE
            )

    if not sub_questions:
        logger.warning(
            "Unable to parse sub-questions, defaulting to the main question."
        )
        sub_questions = [question]

    sub_questions = sub_questions[:5]
    logger.info(f"Final list of {len(sub_questions)} sub-questions: {sub_questions}")

    return analysis, sub_questions


async def analyze_and_generate_sub_questions_v3(
    question: str, history: List[Dict[str, Any]] = None
) -> tuple:
    """
    Combined function to analyze question and generate sub-questions.

    Args:
        question: The main question to analyze
        history: Chat history for context

    Returns:
        Tuple of (analysis, sub_questions)
    """
    # Prepare context from history if available
    context = ""
    if history:
        context = "Previous conversation:\n"
        for item in history[-3:]:  # Use last 3 exchanges for context
            if "question" in item and "answer" in item:
                context += f"User: {item['question']}\nAssistant: {item['answer']}\n\n"

    # Step 1: analysis + sub-question generation
    logger.info(f"Analyzing question: {question}")
    system_prompt = f"""You are Vuetra's Financial/Stock and News AI Expert.

            TASK 1: First, use reasoning to analyze this question:
            "{question}"

            {context}

            Provide a detailed analysis of:
            1. The core information being requested
            2. The domain knowledge required (finance, economics, trading, etc.)
            3. Any implicit assumptions in the question
            4. Any potential ambiguities that need clarification
            5. The type of data that would be most relevant (historical prices, company fundamentals, economic indicators, etc.)

            Start with Analysis title and walk through your reasoning process.
            End with a "Conclusion:" section that summarizes the key points.

            IMPORTANT: Format your summary response in HTML that can be directly rendered in a UI.
            - Respond only in html rich text with tags like <strong>, <p>, <ul>, <li> etc. This should be directly rendered in the UI with innerHTML."
            Use appropriate HTML tags like:
            - <h2> for section headings
            - <p> for paragraphs
            - <ul> and <li> for lists
            - <strong> for emphasis
            - <div> with appropriate classes for different sections

            TASK 2: Based on your analysis, generate 3-5 specific sub-questions that would help gather all the information needed for a comprehensive answer, this should be ouside the inner html, is should be in plan text format.

            Format your sub-questions as a JSON array at the end of your response in plain text, like this:
            SUB_QUESTIONS: ["Question 1?", "Question 2?", "Question 3?"]
            """

    try:
        import time
        start_time = time.time()

        # Create LangChain components
        llm = ChatOpenAI(
            model=settings.GPT_4O_MINI_MODEL_NAME, temperature=0.2, max_tokens=1000
        )

        # Build messages
        messages = [
            SystemMessage(content=system_prompt),
            HumanMessage(content=question),
        ]

        # Create chain
        chain = RunnablePassthrough() | llm | StrOutputParser()

        # Execute chain
        full_text = await chain.ainvoke(messages)
        logger.info("Successfully analyzed question with chain of thought reasoning")

        # Track cost
        duration_ms = (time.time() - start_time) * 1000
        input_text = f"{system_prompt}\n{question}"
        cost_tracker.track_model_usage(
            model_name=settings.GPT_4O_MINI_MODEL_NAME,
            function_name="analyze_and_generate_sub_questions_v3",
            input_text=input_text,
            output_text=full_text,
            duration_ms=duration_ms,
            reasoning_effort="high"
        )

    except Exception as e:
        logger.warning(f"Error using LangChain: {str(e)}")
        raise e

    # Extract analysis (everything before SUB_QUESTIONS)
    sub_q_match = re.search(
        r"(?i)(?:SUB_QUESTIONS|QUESTIONS)\s*[:\-]\s*(\[.*\])",
        full_text,
        flags=re.DOTALL,
    )

    if sub_q_match:
        analysis = full_text[: sub_q_match.start()].strip()
        try:
            sub_questions = json.loads(sub_q_match.group(1))
        except json.JSONDecodeError:
            sub_questions = re.findall(r'"([^"]*\?)"', sub_q_match.group(1))
    else:
        # Fallback parsing
        analysis = full_text
        sub_questions = re.findall(r"\d+\.\s*(.*?\?)", full_text)
        if not sub_questions:
            sub_questions = re.findall(
                r"^(?:-|\*)\s*(.+\?)$", full_text, flags=re.MULTILINE
            )

    if not sub_questions:
        logger.warning(
            "Unable to parse sub-questions, defaulting to the main question."
        )
        sub_questions = [question]

    sub_questions = sub_questions[:5]
    logger.info(f"Final list of {len(sub_questions)} sub-questions: {sub_questions}")

    return analysis, sub_questions


def fetch_subq_sonar_answer(
    sub_q: str,
    date_context: str,
    current_date: datetime,
) -> Dict[str, Any]:
    """
    Synchronously fetch research for one sub-question using Perplexity Sonar model.

    Args:
        sub_q: The sub-question to research
        date_context: Date context string
        current_date: Current date object

    Returns:
        Dictionary with question and answer
    """
    logger.info(f"Researching sub-question with Sonar: {sub_q}")

    try:
        import time
        start_time = time.time()

        # Create the full system prompt with date context
        full_system_prompt = system_prompt_stream.format(
            date_context=date_context, current_date=current_date
        )

        messages = [
            {"role": "system", "content": full_system_prompt},
            {"role": "user", "content": sub_q},
        ]

        # Use the perplexity_client with streaming disabled for synchronous response
        response = perplexity_client.chat.completions.create(
            model=settings.PERPLEXITY_MODEL_NAME,
            messages=messages,
            web_search_options={"search_context_size": "low"},
            stream=False,  # Synchronous response
            temperature=0.4,
            max_tokens=3048,
        )

        answer = response.choices[0].message.content
        # Clean up the answer

        answer = re.sub(r"\?utm_source=openai", "", answer)
        answer = (
            answer.replace("\\", "").replace("```html", "").replace("```", "").strip()
        )

        # Track cost
        duration_ms = (time.time() - start_time) * 1000
        input_text = f"{full_system_prompt}\n{sub_q}"
        cost_tracker.track_model_usage(
            model_name=settings.PERPLEXITY_MODEL_NAME,
            function_name="fetch_subq_sonar_answer",
            input_text=input_text,
            output_text=answer,
            duration_ms=duration_ms
        )

        # Get citations if available
        citations = getattr(response, "citations", [])

        return {
            "question": sub_q,
            "answer": answer.replace("```html", "").replace("```", "").strip(),
            "citations": citations,
        }

    except Exception as e:
        error_msg = f"Error executing Perplexity Sonar search for '{sub_q}': {str(e)}"
        logger.error(error_msg)
        # Return error as answer to maintain consistency
        return {"question": sub_q, "answer": f"<p>Error: {error_msg}</p>"}


def fetch_subq_sonar_answer_v3(
    sub_q: str,
    date_context: str,
    current_date: datetime,
) -> Dict[str, Any]:
    """
    Synchronously fetch research for one sub-question using Perplexity Sonar model.

    Args:
        sub_q: The sub-question to research
        date_context: Date context string
        current_date: Current date object

    Returns:
        Dictionary with question and answer
    """
    logger.info(f"Researching sub-question with Sonar: {sub_q}")

    try:
        # Create the full system prompt with date context
        full_system_prompt = system_prompt_stream.format(
            date_context=date_context, current_date=current_date
        )

        # Create LangChain components
        llm = ChatPerplexity(
            model=settings.PERPLEXITY_MODEL_NAME,
            pplx_api_key=settings.PERPLEXITY_KEY,
            web_search_options={"search_context_size": "low"},
            temperature=0.4,
            max_tokens=3048,
            streaming=False,
        )

        # Build messages
        messages = [
            SystemMessage(content=full_system_prompt),
            HumanMessage(content=sub_q),
        ]

        # Create chain
        chain = RunnablePassthrough() | llm | StrOutputParser()

        # Execute chain synchronously
        answer = chain.invoke(messages)

        # Clean up the answer

        answer = re.sub(r"\?utm_source=openai", "", answer)
        answer = (
            answer.replace("\\", "").replace("```html", "").replace("```", "").strip()
        )

        # Note: Citations may not be directly available through LangChain wrapper
        # You may need to access them differently depending on the implementation
        citations = []

        return {"question": sub_q, "answer": answer, "citations": citations}

    except Exception as e:
        error_msg = f"Error executing Perplexity Sonar search for '{sub_q}': {str(e)}"
        logger.error(error_msg)
        # Return error as answer to maintain consistency
        return {"question": sub_q, "answer": f"<p>Error: {error_msg}</p>"}


async def research_sub_questions_parallel(
    sub_questions: List[str],
) -> List[Dict[str, Any]]:
    """
    Research all sub-questions in parallel using ThreadPoolExecutor.

    Args:
        sub_questions: List of sub-questions to research

    Returns:
        List of research results
    """
    date_context = f"Today's date is {datetime.today():%Y-%m-%d}"
    current_date = datetime.today()

    results: List[Dict[str, Any]] = []

    logger.info("Using Perplexity Sonar model for sub-question research")

    with ThreadPoolExecutor(max_workers=len(sub_questions)) as pool:
        futures = {
            pool.submit(fetch_subq_sonar_answer, sq, date_context, current_date): sq
            for sq in sub_questions
        }
        for fut in as_completed(futures):
            try:
                results.append(fut.result())
            except Exception as e:
                logger.error(f"Error researching '{futures[fut]}': {e}")
                # Add error result to maintain consistency
                results.append(
                    {
                        "question": futures[fut],
                        "answer": f"<p>Error researching this question: {str(e)}</p>",
                    }
                )

    return results


async def research_sub_questions_parallel_v3(
    sub_questions: List[str],
) -> List[Dict[str, Any]]:
    """
    Research all sub-questions in parallel using ThreadPoolExecutor.

    Args:
        sub_questions: List of sub-questions to research

    Returns:
        List of research results
    """
    date_context = f"Today's date is {datetime.today():%Y-%m-%d}"
    current_date = datetime.today()

    results: List[Dict[str, Any]] = []

    logger.info("Using Perplexity Sonar model for sub-question research")

    with ThreadPoolExecutor(max_workers=len(sub_questions)) as pool:
        futures = {
            pool.submit(fetch_subq_sonar_answer_v3, sq, date_context, current_date): sq
            for sq in sub_questions
        }
        for fut in as_completed(futures):
            try:
                results.append(fut.result())
            except Exception as e:
                logger.error(f"Error researching '{futures[fut]}': {e}")
                # Add error result to maintain consistency
                results.append(
                    {
                        "question": futures[fut],
                        "answer": f"<p>Error researching this question: {str(e)}</p>",
                    }
                )

    return results


async def synthesize_findings(
    question: str, research_results: List[Dict[str, str]]
) -> str:
    """
    Synthesizes the research findings into a comprehensive, HTML-formatted answer.
    """
    # Build the concatenated research text
    research_text = ""
    for i, result in enumerate(research_results, start=1):
        research_text += (
            f"Sub-question {i}: {result['question']}\n"
            f"Findings: {result['answer']}\n\n"
        )

    # Date context
    current_date = datetime.now()
    date_context = f"Current date: {current_date:%B %d, %Y}"

    # System and user messages for final synthesis
    system_content = f"""You are Vuetra's Financial/Stock and News AI Expert. Use deep reasoning with high effort.

{date_context}

Original question: {question}

Research findings:
{research_text}

Your task is to:
1. Identify the key insights from each sub-question, but no need to mention subquestion this in summary. Generate title for that sub question in summary.
2. Analyze how these insights relate to the main question
3. Evaluate the reliability and relevance of each piece of information
4. Synthesize a comprehensive answer with explicit reasoning
5. Generate a title for the response according to the question that shows your reasoning process
6. Include a 'Comparative Analysis' section if multiple items or concepts are being compared
7. End with a 'Conclusion' section that directly answers the original question

IMPORTANT FOR TIME-SENSITIVE INFORMATION:
- When discussing "latest" or "current" information, explicitly mention that the information is as of {current_date:%B %d, %Y}.
- If the information appears outdated, acknowledge this limitation in your response.
- For financial or market data, specify the date of the information to avoid confusion.

Format your response as HTML using:
- <h2> for main headings 
- <h3> for subsections
- <p> for paragraphs
- <ul> and <li> for lists
- <strong> for emphasis
- <div class="deep-search-result"> as the outer container
"""
    user_content = "Please provide a comprehensive synthesis with deep reasoning."

    input_messages = [
        {"role": "system", "content": system_content},
        {"role": "user", "content": user_content},
    ]

    try:
        import time
        start_time = time.time()

        # Try using o4-mini with high reasoning for synthesis
        enhanced_resp = await asyncio.to_thread(
            client.responses.create,
            # model="o4-mini",
            model=settings.GPT_4O_MINI_MODEL_NAME,
            # reasoning={"effort": "high"},
            input=input_messages,
            max_output_tokens=10000,
        )

        # Track cost for o4-mini
        duration_ms = (time.time() - start_time) * 1000
        input_text = f"{system_content}\n{user_content}"
        cost_tracker.track_model_usage(
            model_name=settings.GPT_4O_MINI_MODEL_NAME,
            function_name="synthesize_findings",
            input_text=input_text,
            output_text=enhanced_resp.output_text,
            duration_ms=duration_ms,
            reasoning_effort="high"
        )

        return enhanced_resp.output_text

    except Exception as e:
        # Fallback to gpt-4o-mini if o4-mini fails
        logger.warning(
            f"Error using o4-mini for synthesis: {str(e)}. Falling back to gpt-4o-mini."
        )

        start_time = time.time()
        response = client.chat.completions.create(
            model=settings.GPT_4O_MINI_MODEL_NAME,
            messages=input_messages,
            temperature=0.3,
            max_tokens=2000,
        )

        # Track cost for fallback
        duration_ms = (time.time() - start_time) * 1000
        track_openai_response(
            model_name=settings.GPT_4O_MINI_MODEL_NAME,
            function_name="synthesize_findings_fallback",
            messages=input_messages,
            response=response,
            duration_ms=duration_ms
        )

        return response.choices[0].message.content


async def synthesize_findings_v3(
    question: str, research_results: List[Dict[str, str]]
) -> str:
    """
    Synthesizes the research findings into a comprehensive, HTML-formatted answer.
    """
    # Build the concatenated research text
    research_text = ""
    for i, result in enumerate(research_results, start=1):
        research_text += (
            f"Sub-question {i}: {result['question']}\n"
            f"Findings: {result['answer']}\n\n"
        )

    # Date context
    current_date = datetime.now()
    date_context = f"Current date: {current_date:%B %d, %Y}"

    # System and user messages for final synthesis
    system_content = f"""You are Vuetra's Financial/Stock and News AI Expert. Use deep reasoning with high effort.

        {date_context}
        
        Original question: {question}
        
        Research findings:
        {research_text}
        
        Your task is to:
        1. Identify the key insights from each sub-question, but no need to mention subquestion this in summary. Generate title for that sub question in summary.
        2. Analyze how these insights relate to the main question
        3. Evaluate the reliability and relevance of each piece of information
        4. Synthesize a comprehensive answer with explicit reasoning
        5. Generate a title for the response according to the question that shows your reasoning process
        6. Include a 'Comparative Analysis' section if multiple items or concepts are being compared
        7. End with a 'Conclusion' section that directly answers the original question
        
        IMPORTANT FOR TIME-SENSITIVE INFORMATION:
        - When discussing "latest" or "current" information, explicitly mention that the information is as of {current_date:%B %d, %Y}.
        - If the information appears outdated, acknowledge this limitation in your response.
        - For financial or market data, specify the date of the information to avoid confusion.
        
        Format your response as HTML using:
        - <h2> for main headings 
        - <h3> for subsections
        - <p> for paragraphs
        - <ul> and <li> for lists
        - <strong> for emphasis
        - <div class="deep-search-result"> as the outer container
        """
    user_content = "Please provide a comprehensive synthesis with deep reasoning."

    try:
        # Try using o4-mini model first
        # llm_enhanced = ChatOpenAI(
        #     model="o4-mini",
        #     max_completion_tokens=10000,
        # )

        llm_enhanced = ChatOpenAI(
            model=settings.GPT_4O_MINI_MODEL_NAME, temperature=0.3, max_tokens=25
        )

        # Build messages
        messages = [
            SystemMessage(content=system_content),
            HumanMessage(content=user_content),
        ]

        # Create chain
        chain_enhanced = RunnablePassthrough() | llm_enhanced | StrOutputParser()

        # Execute chain
        response = await chain_enhanced.ainvoke(messages)
        return response

    except Exception as e:
        # Fallback to gpt-4o-mini if o4-mini fails
        logger.warning(
            f"Error using o4-mini for synthesis: {str(e)}. Falling back to gpt-4o-mini."
        )

        # Create fallback LLM
        llm_fallback = ChatOpenAI(
            model=settings.GPT_4O_MINI_MODEL_NAME, temperature=0.3, max_tokens=2000
        )

        # Build messages
        messages = [
            SystemMessage(content=system_content),
            HumanMessage(content=user_content),
        ]

        # Create fallback chain
        chain_fallback = RunnablePassthrough() | llm_fallback | StrOutputParser()

        # Execute fallback chain
        response = await chain_fallback.ainvoke(messages)
        return response


async def generate_sub_question_titles(
    question: str, sub_questions: List[str]
) -> List[str]:
    """
    Generate concise titles for sub-questions using AI.
    Returns a list of titles, one for each sub-question.
    """
    titles = []

    for sub_q in sub_questions:
        try:
            system_prompt = f"""You are a title generator for financial analysis sections.

            Main question: {question}
            Sub-question: {sub_q}

            Generate a very concise, descriptive title (2-4 words) that captures what this sub-question is analyzing.

            Examples:
            - "Market Analysis"
            - "Price Trends"
            - "Technical Indicators"
            - "Risk Assessment"
            - "Trading Strategies"

            Respond with ONLY the title, no quotes or extra text."""

            resp = client.chat.completions.create(
                model=settings.GPT_4O_MINI_MODEL_NAME,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": f"Generate title for: {sub_q}"},
                ],
                temperature=0.3,
                max_tokens=25,
            )

            title = resp.choices[0].message.content.strip()
            # Remove any quotes that might be added by the AI
            title = title.strip('"').strip("'")
            titles.append(title)

        except Exception as e:
            logger.error(f"Error generating title for sub-question '{sub_q}': {e}")
            # Fallback: use first few words of sub-question
            fallback_title = " ".join(sub_q.split()[:3]).replace("?", "")
            titles.append(fallback_title)

    return titles


async def generate_synthesis_title(question: str, sub_questions: List[str]) -> str:
    """
    Generate an overarching title for the final synthesis.
    """
    try:
        sub_q_text = "\n".join([f"- {sq}" for sq in sub_questions])

        system_prompt = f"""You are a title generator for comprehensive financial analysis reports.

Main question: {question}

Sub-questions analyzed:
{sub_q_text}

Generate a comprehensive, professional title (3-6 words) that captures the overall analysis being performed.

Examples:
- "Comprehensive Market Analysis"
- "Investment Strategy Assessment"
- "Technical Analysis Report"
- "Risk and Opportunity Review"

Respond with ONLY the title, no quotes or extra text."""

        resp = client.chat.completions.create(
            model=settings.GPT_4O_MINI_MODEL_NAME,
            messages=[
                {"role": "system", "content": system_prompt},
                {
                    "role": "user",
                    "content": f"Generate comprehensive title for this analysis",
                },
            ],
            temperature=0.3,
            max_tokens=30,
        )

        title = resp.choices[0].message.content.strip()
        # Remove any quotes that might be added by the AI
        return title.strip('"').strip("'")

    except Exception as e:
        logger.error(f"Error generating synthesis title: {e}")
        # Fallback: use main question
        return f"Analysis: {question[:50]}{'...' if len(question) > 50 else ''}"


async def generate_synthesis_title_v3(question: str, sub_questions: List[str]) -> str:
    """
    Generate an overarching title for the final synthesis.
    """
    try:
        sub_q_text = "\n".join([f"- {sq}" for sq in sub_questions])

        system_prompt = f"""You are a title generator for comprehensive financial analysis reports.

        Main question: {question}
        
        Sub-questions analyzed:
        {sub_q_text}
        
        Generate a comprehensive, professional title (3-6 words) that captures the overall analysis being performed.
        
        Examples:
        - "Comprehensive Market Analysis"
        - "Investment Strategy Assessment"
        - "Technical Analysis Report"
        - "Risk and Opportunity Review"
        
        Respond with ONLY the title, no quotes or extra text."""

        # Create LangChain components
        llm = ChatOpenAI(
            model=settings.GPT_4O_MINI_MODEL_NAME, temperature=0.3, max_tokens=30
        )

        # Build messages
        messages = [
            SystemMessage(content=system_prompt),
            HumanMessage(content="Generate comprehensive title for this analysis"),
        ]

        # Create chain
        chain = RunnablePassthrough() | llm | StrOutputParser()

        # Execute chain
        response = await chain.ainvoke(messages)

        title = response.strip()
        # Remove any quotes that might be added by the AI
        return title.strip('"').strip("'")

    except Exception as e:
        logger.error(f"Error generating synthesis title: {e}")
        # Fallback: use main question
        return f"Analysis: {question[:50]}{'...' if len(question) > 50 else ''}"


async def generate_sub_question_titles_v3(
    question: str, sub_questions: List[str]
) -> List[str]:
    """
    Generate concise titles for sub-questions using AI.
    Returns a list of titles, one for each sub-question.
    """
    titles = []

    # Create LangChain components
    llm = ChatOpenAI(
        model=settings.GPT_4O_MINI_MODEL_NAME, temperature=0.3, max_tokens=25
    )

    for sub_q in sub_questions:
        try:
            system_prompt = f"""You are a title generator for financial analysis sections.

            Main question: {question}
            Sub-question: {sub_q}

            Generate a very concise, descriptive title (2-4 words) that captures what this sub-question is analyzing.

            Examples:
            - "Market Analysis"
            - "Price Trends"
            - "Technical Indicators"
            - "Risk Assessment"
            - "Trading Strategies"

            Respond with ONLY the title, no quotes or extra text."""

            # Build messages
            messages = [
                SystemMessage(content=system_prompt),
                HumanMessage(content=f"Generate title for: {sub_q}"),
            ]

            # Create chain
            chain = RunnablePassthrough() | llm | StrOutputParser()

            # Execute chain
            response = await chain.ainvoke(messages)

            title = response.strip()
            # Remove any quotes that might be added by the AI
            title = title.strip('"').strip("'")
            titles.append(title)

        except Exception as e:
            logger.error(f"Error generating title for sub-question '{sub_q}': {e}")
            # Fallback: use first few words of sub-question
            fallback_title = " ".join(sub_q.split()[:3]).replace("?", "")
            titles.append(fallback_title)

    return titles
