"""
Error Monitoring and Alerting System

This module provides comprehensive error monitoring, logging, and alerting
for proactive error detection and response.
"""

import asyncio
import functools
import json
import logging
import time
from collections import defaultdict, deque
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional

from app.services.mail_service import send_alert_mail
from app.utils.error_handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ErrorSeverity, StandardizedError
from app.utils.logger import chatbot_logger as logger


class ErrorMetrics:
    """Track error metrics and patterns"""
    
    def __init__(self, window_minutes: int = 60):
        self.window_minutes = window_minutes
        self.error_counts = defaultdict(int)
        self.error_history = deque()
        self.error_patterns = defaultdict(list)
        self.last_alert_times = defaultdict(float)
        self.alert_cooldown = 300  # 5 minutes cooldown between alerts
    
    def record_error(self, error: StandardizedError, context: Optional[Dict[str, Any]] = None):
        """
        Record an error occurrence
        
        Args:
            error: The standardized error
            context: Additional context information
        """
        timestamp = time.time()
        
        error_record = {
            "timestamp": timestamp,
            "code": error.code.value,
            "category": error.category.value,
            "severity": error.severity.value,
            "message": error.message,
            "context": context or {}
        }
        
        # Add to history
        self.error_history.append(error_record)
        
        # Clean old entries
        self._clean_old_entries()
        
        # Update counts
        self.error_counts[error.code.value] += 1
        self.error_counts[f"category_{error.category.value}"] += 1
        self.error_counts[f"severity_{error.severity.value}"] += 1
        
        # Track patterns
        self.error_patterns[error.code.value].append(timestamp)
        
        # Check for alert conditions
        self._check_alert_conditions(error, error_record)
    
    def _clean_old_entries(self):
        """Remove entries older than the window"""
        cutoff_time = time.time() - (self.window_minutes * 60)
        
        # Clean history
        while self.error_history and self.error_history[0]["timestamp"] < cutoff_time:
            old_record = self.error_history.popleft()
            
            # Decrement counts
            self.error_counts[old_record["code"]] -= 1
            self.error_counts[f"category_{old_record['category']}"] -= 1
            self.error_counts[f"severity_{old_record['severity']}"] -= 1
        
        # Clean patterns
        for code, timestamps in self.error_patterns.items():
            self.error_patterns[code] = [t for t in timestamps if t >= cutoff_time]
    
    def _check_alert_conditions(self, error: StandardizedError, error_record: Dict[str, Any]):
        """
        Check if alert conditions are met
        
        Args:
            error: The standardized error
            error_record: The error record
        """
        current_time = time.time()
        
        # Critical errors always trigger alerts
        if error.severity == ErrorSeverity.CRITICAL:
            self._send_alert(
                "Critical Error Detected",
                f"Critical error occurred: {error.message}",
                error_record,
                "critical"
            )
            return
        
        # High frequency error detection
        error_count = self.error_counts[error.code.value]
        if error_count >= 10:  # 10 errors of same type in window
            alert_key = f"frequency_{error.code.value}"
            if current_time - self.last_alert_times[alert_key] > self.alert_cooldown:
                self._send_alert(
                    "High Frequency Error Detected",
                    f"Error {error.code.value} occurred {error_count} times in {self.window_minutes} minutes",
                    error_record,
                    "high_frequency"
                )
                self.last_alert_times[alert_key] = current_time
        
        # Error spike detection
        recent_errors = len([e for e in self.error_history if current_time - e["timestamp"] < 300])  # 5 minutes
        if recent_errors >= 20:  # 20 errors in 5 minutes
            alert_key = "error_spike"
            if current_time - self.last_alert_times[alert_key] > self.alert_cooldown:
                self._send_alert(
                    "Error Spike Detected",
                    f"{recent_errors} errors occurred in the last 5 minutes",
                    {"recent_errors": recent_errors, "total_errors": len(self.error_history)},
                    "spike"
                )
                self.last_alert_times[alert_key] = current_time
        
        # External API failure pattern
        if error.category == ErrorCategory.EXTERNAL_API:
            api_errors = self.error_counts[f"category_{ErrorCategory.EXTERNAL_API.value}"]
            if api_errors >= 5:  # 5 API errors in window
                alert_key = "api_failures"
                if current_time - self.last_alert_times[alert_key] > self.alert_cooldown:
                    self._send_alert(
                        "External API Failures",
                        f"{api_errors} external API errors in {self.window_minutes} minutes",
                        error_record,
                        "api_failure"
                    )
                    self.last_alert_times[alert_key] = current_time
    
    def _send_alert(self, title: str, message: str, context: Dict[str, Any], alert_type: str):
        """
        Send alert notification
        
        Args:
            title: Alert title
            message: Alert message
            context: Error context
            alert_type: Type of alert
        """
        try:
            alert_data = {
                "title": title,
                "message": message,
                "alert_type": alert_type,
                "timestamp": datetime.utcnow().isoformat(),
                "context": context,
                "metrics": self.get_current_metrics()
            }
            
            # Log the alert
            logger.error(f"ALERT: {title} - {message}")
            logger.error(f"Alert context: {json.dumps(alert_data, indent=2)}")
            
            # Send email alert
            email_body = f"""
            Alert: {title}
            
            Message: {message}
            
            Alert Type: {alert_type}
            Timestamp: {alert_data['timestamp']}
            
            Context:
            {json.dumps(context, indent=2)}
            
            Current Metrics:
            {json.dumps(alert_data['metrics'], indent=2)}
            """
            
            send_alert_mail(email_body)
            
        except Exception as e:
            logger.error(f"Failed to send alert: {e}")
    
    def get_current_metrics(self) -> Dict[str, Any]:
        """
        Get current error metrics
        
        Returns:
            Dictionary of current metrics
        """
        return {
            "total_errors": len(self.error_history),
            "error_counts": dict(self.error_counts),
            "window_minutes": self.window_minutes,
            "top_errors": self._get_top_errors(),
            "error_rate": len(self.error_history) / max(self.window_minutes, 1)
        }
    
    def _get_top_errors(self, limit: int = 5) -> List[Dict[str, Any]]:
        """Get top error codes by frequency"""
        error_code_counts = {
            code: count for code, count in self.error_counts.items()
            if not code.startswith("category_") and not code.startswith("severity_")
        }
        
        sorted_errors = sorted(error_code_counts.items(), key=lambda x: x[1], reverse=True)
        return [{"code": code, "count": count} for code, count in sorted_errors[:limit]]


class ErrorMonitor:
    """Main error monitoring system"""
    
    _instance = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._instance.metrics = ErrorMetrics()
            cls._instance.is_monitoring = False
        return cls._instance
    
    @classmethod
    def get_instance(cls) -> "ErrorMonitor":
        """Get singleton instance"""
        return cls()
    
    def start_monitoring(self):
        """Start the error monitoring system"""
        if not self.is_monitoring:
            self.is_monitoring = True
            logger.info("Error monitoring system started")
    
    def stop_monitoring(self):
        """Stop the error monitoring system"""
        self.is_monitoring = False
        logger.info("Error monitoring system stopped")
    
    def record_error(self, error: StandardizedError, context: Optional[Dict[str, Any]] = None):
        """
        Record an error for monitoring
        
        Args:
            error: The standardized error
            context: Additional context information
        """
        if self.is_monitoring:
            self.metrics.record_error(error, context)
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get current error metrics"""
        return self.metrics.get_current_metrics()
    
    def get_health_status(self) -> Dict[str, Any]:
        """
        Get system health status based on error patterns
        
        Returns:
            Health status information
        """
        metrics = self.get_metrics()
        total_errors = metrics["total_errors"]
        error_rate = metrics["error_rate"]
        
        # Determine health status
        if error_rate > 10:  # More than 10 errors per minute
            status = "critical"
        elif error_rate > 5:  # More than 5 errors per minute
            status = "warning"
        elif error_rate > 1:  # More than 1 error per minute
            status = "degraded"
        else:
            status = "healthy"
        
        return {
            "status": status,
            "error_rate": error_rate,
            "total_errors": total_errors,
            "metrics": metrics,
            "timestamp": datetime.utcnow().isoformat()
        }


# Global error monitor instance
error_monitor = ErrorMonitor.get_instance()


def monitor_error(error: StandardizedError, context: Optional[Dict[str, Any]] = None):
    """
    Convenience function to record an error for monitoring
    
    Args:
        error: The standardized error
        context: Additional context information
    """
    error_monitor.record_error(error, context)


def get_error_metrics() -> Dict[str, Any]:
    """Get current error metrics"""
    return error_monitor.get_metrics()


def get_health_status() -> Dict[str, Any]:
    """Get system health status"""
    return error_monitor.get_health_status()


# Decorator to automatically monitor errors
def with_error_monitoring(context_func: Optional[callable] = None):
    """
    Decorator to automatically monitor errors from functions
    
    Args:
        context_func: Optional function to extract context from function args
    """
    def decorator(func):
        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs):
            try:
                return await func(*args, **kwargs)
            except StandardizedError as e:
                context = context_func(*args, **kwargs) if context_func else None
                monitor_error(e, context)
                raise e
            except Exception as e:
                # Convert to standardized error and monitor
                from app.utils.error_handler import ErrorCode, ErrorCategory, ErrorSeverity
                
                standardized_error = StandardizedError(
                    code=ErrorCode.INTERNAL_SERVER_ERROR,
                    message=str(e),
                    category=ErrorCategory.SYSTEM,
                    severity=ErrorSeverity.HIGH,
                    original_exception=e
                )
                
                context = context_func(*args, **kwargs) if context_func else None
                monitor_error(standardized_error, context)
                raise standardized_error
        
        @functools.wraps(func)
        def sync_wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except StandardizedError as e:
                context = context_func(*args, **kwargs) if context_func else None
                monitor_error(e, context)
                raise e
            except Exception as e:
                # Convert to standardized error and monitor
                from app.utils.error_handler import ErrorCode, ErrorCategory, ErrorSeverity
                
                standardized_error = StandardizedError(
                    code=ErrorCode.INTERNAL_SERVER_ERROR,
                    message=str(e),
                    category=ErrorCategory.SYSTEM,
                    severity=ErrorSeverity.HIGH,
                    original_exception=e
                )
                
                context = context_func(*args, **kwargs) if context_func else None
                monitor_error(standardized_error, context)
                raise standardized_error
        
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        return sync_wrapper
    
    return decorator
