"""
Validation Error Handling Utilities

This module provides comprehensive input validation and error handling
for all API endpoints with detailed error messages.
"""

import asyncio
import functools
import logging
from typing import Any, Callable, Dict, List, Optional, Type, Union

from fastapi import HTTPException, Request, UploadFile
from pydantic import BaseModel, ValidationError as PydanticValidationError

from app.utils.error_handler import (
    ErrorCategory,
    ErrorCode,
    Error<PERSON><PERSON>ler,
    Error<PERSON>ever<PERSON>,
    ValidationError,
)
from app.utils.logger import chatbot_logger as logger


class ValidationErrorHandler:
    """Centralized validation error handling"""
    
    @staticmethod
    def format_pydantic_errors(errors: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Format Pydantic validation errors into user-friendly messages
        
        Args:
            errors: List of Pydantic validation errors
            
        Returns:
            List of formatted error dictionaries
        """
        formatted_errors = []
        
        for error in errors:
            field_path = " -> ".join(str(loc) for loc in error["loc"])
            error_type = error["type"]
            message = error["msg"]
            
            # Create user-friendly messages based on error type
            if error_type == "missing":
                user_message = f"Field '{field_path}' is required"
            elif error_type == "type_error":
                user_message = f"Field '{field_path}' has invalid type: {message}"
            elif error_type == "value_error":
                user_message = f"Field '{field_path}' has invalid value: {message}"
            elif error_type.startswith("string"):
                user_message = f"Field '{field_path}' string validation failed: {message}"
            elif error_type.startswith("number"):
                user_message = f"Field '{field_path}' number validation failed: {message}"
            else:
                user_message = f"Field '{field_path}': {message}"
            
            formatted_errors.append({
                "field": field_path,
                "type": error_type,
                "message": user_message,
                "input_value": error.get("input")
            })
        
        return formatted_errors
    
    @staticmethod
    def create_validation_error(
        message: str,
        field: Optional[str] = None,
        errors: Optional[List[Dict[str, Any]]] = None
    ) -> ValidationError:
        """
        Create a standardized validation error
        
        Args:
            message: Main error message
            field: Field that failed validation
            errors: List of detailed validation errors
            
        Returns:
            ValidationError: Standardized validation error
        """
        additional_info = {}
        if errors:
            additional_info["validation_errors"] = errors
        
        return ValidationError(
            message=message,
            field=field,
            additional_info=additional_info
        )


def validate_request_data(schema: Type[BaseModel]):
    """
    Decorator to validate request data against a Pydantic schema
    
    Args:
        schema: Pydantic model class for validation
    """
    def decorator(func):
        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs):
            try:
                # Find the data parameter in kwargs
                data = None
                for key, value in kwargs.items():
                    if isinstance(value, dict) or hasattr(value, '__dict__'):
                        try:
                            # Try to validate with the schema
                            validated_data = schema(**value if isinstance(value, dict) else value.__dict__)
                            kwargs[key] = validated_data
                            break
                        except (TypeError, AttributeError):
                            continue
                
                return await func(*args, **kwargs)
                
            except PydanticValidationError as e:
                logger.warning(f"Validation error in {func.__name__}: {e}")
                
                formatted_errors = ValidationErrorHandler.format_pydantic_errors(e.errors())
                validation_error = ValidationErrorHandler.create_validation_error(
                    message="Request validation failed",
                    errors=formatted_errors
                )
                
                # Extract request from args if available
                request = None
                for arg in args:
                    if isinstance(arg, Request):
                        request = arg
                        break
                
                return ErrorHandler.create_error_response(validation_error, request)
            
            except Exception as e:
                logger.error(f"Unexpected error in validation: {e}")
                validation_error = ValidationErrorHandler.create_validation_error(
                    message=f"Unexpected validation error: {str(e)}"
                )
                return ErrorHandler.create_error_response(validation_error)
        
        @functools.wraps(func)
        def sync_wrapper(*args, **kwargs):
            try:
                # Find the data parameter in kwargs
                for key, value in kwargs.items():
                    if isinstance(value, dict) or hasattr(value, '__dict__'):
                        try:
                            # Try to validate with the schema
                            validated_data = schema(**value if isinstance(value, dict) else value.__dict__)
                            kwargs[key] = validated_data
                            break
                        except (TypeError, AttributeError):
                            continue
                
                return func(*args, **kwargs)
                
            except PydanticValidationError as e:
                logger.warning(f"Validation error in {func.__name__}: {e}")
                
                formatted_errors = ValidationErrorHandler.format_pydantic_errors(e.errors())
                validation_error = ValidationErrorHandler.create_validation_error(
                    message="Request validation failed",
                    errors=formatted_errors
                )
                
                return ErrorHandler.create_error_response(validation_error)
            
            except Exception as e:
                logger.error(f"Unexpected error in validation: {e}")
                validation_error = ValidationErrorHandler.create_validation_error(
                    message=f"Unexpected validation error: {str(e)}"
                )
                return ErrorHandler.create_error_response(validation_error)
        
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        return sync_wrapper
    
    return decorator


def validate_file_upload(
    allowed_types: List[str],
    max_size_mb: int = 10,
    required: bool = True
):
    """
    Decorator to validate file uploads
    
    Args:
        allowed_types: List of allowed MIME types
        max_size_mb: Maximum file size in MB
        required: Whether file is required
    """
    def decorator(func):
        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs):
            try:
                # Find file parameter in kwargs
                file = None
                file_param_name = None
                
                for key, value in kwargs.items():
                    if isinstance(value, UploadFile):
                        file = value
                        file_param_name = key
                        break
                
                if required and not file:
                    validation_error = ValidationErrorHandler.create_validation_error(
                        message="File upload is required",
                        field="file"
                    )
                    return ErrorHandler.create_error_response(validation_error)
                
                if file:
                    # Validate file type
                    if file.content_type not in allowed_types:
                        validation_error = ValidationErrorHandler.create_validation_error(
                            message=f"Invalid file type. Allowed types: {', '.join(allowed_types)}",
                            field="file",
                            additional_info={
                                "provided_type": file.content_type,
                                "allowed_types": allowed_types
                            }
                        )
                        return ErrorHandler.create_error_response(validation_error)
                    
                    # Validate file size
                    file_size_mb = len(await file.read()) / (1024 * 1024)
                    await file.seek(0)  # Reset file pointer
                    
                    if file_size_mb > max_size_mb:
                        validation_error = ValidationErrorHandler.create_validation_error(
                            message=f"File size exceeds maximum allowed size of {max_size_mb}MB",
                            field="file",
                            additional_info={
                                "file_size_mb": file_size_mb,
                                "max_size_mb": max_size_mb
                            }
                        )
                        return ErrorHandler.create_error_response(validation_error)
                
                return await func(*args, **kwargs)
                
            except Exception as e:
                logger.error(f"File validation error: {e}")
                validation_error = ValidationErrorHandler.create_validation_error(
                    message=f"File validation error: {str(e)}",
                    field="file"
                )
                return ErrorHandler.create_error_response(validation_error)
        
        return async_wrapper
    
    return decorator


def validate_query_parameters(**param_validators):
    """
    Decorator to validate query parameters
    
    Args:
        **param_validators: Dictionary of parameter name to validator function
    """
    def decorator(func):
        @functools.wraps(func)
        async def async_wrapper(request: Request, *args, **kwargs):
            try:
                validation_errors = []
                
                for param_name, validator in param_validators.items():
                    param_value = request.query_params.get(param_name)
                    
                    try:
                        if param_value is not None:
                            validated_value = validator(param_value)
                            kwargs[param_name] = validated_value
                    except ValueError as e:
                        validation_errors.append({
                            "field": param_name,
                            "type": "value_error",
                            "message": f"Invalid value for parameter '{param_name}': {str(e)}",
                            "input_value": param_value
                        })
                
                if validation_errors:
                    validation_error = ValidationErrorHandler.create_validation_error(
                        message="Query parameter validation failed",
                        errors=validation_errors
                    )
                    return ErrorHandler.create_error_response(validation_error, request)
                
                return await func(request, *args, **kwargs)
                
            except Exception as e:
                logger.error(f"Query parameter validation error: {e}")
                validation_error = ValidationErrorHandler.create_validation_error(
                    message=f"Query parameter validation error: {str(e)}"
                )
                return ErrorHandler.create_error_response(validation_error, request)
        
        return async_wrapper
    
    return decorator


# Common validators
def validate_positive_int(value: str) -> int:
    """Validate that a string represents a positive integer"""
    try:
        int_value = int(value)
        if int_value <= 0:
            raise ValueError("Value must be positive")
        return int_value
    except ValueError:
        raise ValueError("Value must be a positive integer")


def validate_email(value: str) -> str:
    """Validate email format"""
    import re
    email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    if not re.match(email_pattern, value):
        raise ValueError("Invalid email format")
    return value


def validate_uuid(value: str) -> str:
    """Validate UUID format"""
    import uuid
    try:
        uuid.UUID(value)
        return value
    except ValueError:
        raise ValueError("Invalid UUID format")
