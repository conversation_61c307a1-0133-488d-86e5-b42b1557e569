"""
Database Error Handling Utilities

This module provides robust error handling for database operations with proper
transaction management, connection pooling, and standardized error responses.
"""

import asyncio
import functools
import logging
from contextlib import asynccontextmanager
from typing import Any, AsyncGenerator, Callable, Optional, TypeVar

import pymysql
from fastapi import HTTPException
from sqlalchemy.exc import (
    DatabaseError as SQLDatabaseError,
    DisconnectionError,
    IntegrityError,
    OperationalError,
    SQLAlchemyError,
    TimeoutError,
)
from sqlalchemy.ext.asyncio import AsyncSession
from tenacity import (
    retry,
    retry_if_exception_type,
    stop_after_attempt,
    wait_exponential,
)

from app.utils.error_handler import (
    DatabaseError,
    ErrorCategory,
    ErrorCode,
    ErrorSeverity,
    StandardizedError,
)
from app.utils.logger import chatbot_logger as logger

# Type variable for generic function return type
T = TypeVar("T")


class DatabaseErrorHandler:
    """Centralized database error handling"""
    
    @staticmethod
    def classify_database_error(error: Exception) -> DatabaseError:
        """
        Classify database errors and return appropriate DatabaseError
        
        Args:
            error: The original database exception
            
        Returns:
            DatabaseError: Standardized database error
        """
        if isinstance(error, (pymysql.err.OperationalError, OperationalError)):
            if "timeout" in str(error).lower():
                return DatabaseError(
                    message="Database operation timed out",
                    operation="query_execution",
                    code=ErrorCode.DATABASE_TIMEOUT,
                    severity=ErrorSeverity.HIGH,
                    original_exception=error
                )
            elif "connection" in str(error).lower():
                return DatabaseError(
                    message="Database connection failed",
                    operation="connection",
                    code=ErrorCode.DATABASE_CONNECTION_ERROR,
                    severity=ErrorSeverity.CRITICAL,
                    original_exception=error
                )
            else:
                return DatabaseError(
                    message=f"Database operational error: {str(error)}",
                    operation="query_execution",
                    severity=ErrorSeverity.HIGH,
                    original_exception=error
                )
        
        elif isinstance(error, IntegrityError):
            return DatabaseError(
                message="Database constraint violation",
                operation="data_integrity",
                code=ErrorCode.DATABASE_CONSTRAINT_VIOLATION,
                severity=ErrorSeverity.MEDIUM,
                original_exception=error,
                additional_info={"constraint_type": "integrity"}
            )
        
        elif isinstance(error, TimeoutError):
            return DatabaseError(
                message="Database query timeout",
                operation="query_execution",
                code=ErrorCode.DATABASE_TIMEOUT,
                severity=ErrorSeverity.HIGH,
                original_exception=error
            )
        
        elif isinstance(error, DisconnectionError):
            return DatabaseError(
                message="Database connection lost",
                operation="connection",
                code=ErrorCode.DATABASE_CONNECTION_ERROR,
                severity=ErrorSeverity.CRITICAL,
                original_exception=error
            )
        
        elif isinstance(error, SQLAlchemyError):
            return DatabaseError(
                message=f"Database error: {str(error)}",
                operation="query_execution",
                severity=ErrorSeverity.HIGH,
                original_exception=error
            )
        
        else:
            return DatabaseError(
                message=f"Unexpected database error: {str(error)}",
                operation="unknown",
                severity=ErrorSeverity.HIGH,
                original_exception=error
            )


def handle_database_errors(operation: str = "database_operation"):
    """
    Decorator to handle database errors with proper classification
    
    Args:
        operation: Description of the database operation being performed
    """
    def decorator(func):
        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs):
            try:
                return await func(*args, **kwargs)
            except Exception as e:
                db_error = DatabaseErrorHandler.classify_database_error(e)
                db_error.additional_info = db_error.additional_info or {}
                db_error.additional_info["operation"] = operation
                raise db_error
        
        @functools.wraps(func)
        def sync_wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                db_error = DatabaseErrorHandler.classify_database_error(e)
                db_error.additional_info = db_error.additional_info or {}
                db_error.additional_info["operation"] = operation
                raise db_error
        
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        return sync_wrapper
    
    return decorator


@asynccontextmanager
async def safe_database_transaction(session: AsyncSession) -> AsyncGenerator[AsyncSession, None]:
    """
    Context manager for safe database transactions with automatic rollback
    
    Args:
        session: Database session
        
    Yields:
        AsyncSession: The database session within transaction context
        
    Raises:
        DatabaseError: If transaction fails
    """
    try:
        await session.begin()
        logger.debug("Database transaction started")
        
        yield session
        
        await session.commit()
        logger.debug("Database transaction committed successfully")
        
    except Exception as e:
        logger.error(f"Database transaction failed, rolling back: {str(e)}")
        try:
            await session.rollback()
            logger.debug("Database transaction rolled back successfully")
        except Exception as rollback_error:
            logger.error(f"Failed to rollback transaction: {str(rollback_error)}")
        
        # Classify and raise the original error
        db_error = DatabaseErrorHandler.classify_database_error(e)
        db_error.additional_info = db_error.additional_info or {}
        db_error.additional_info["transaction_rolled_back"] = True
        raise db_error


@retry(
    retry=retry_if_exception_type((OperationalError, DisconnectionError, TimeoutError)),
    stop=stop_after_attempt(3),
    wait=wait_exponential(multiplier=1, min=1, max=10),
    reraise=True
)
async def execute_with_retry(session: AsyncSession, operation: Callable[[], T]) -> T:
    """
    Execute database operation with retry logic for transient failures
    
    Args:
        session: Database session
        operation: Database operation to execute
        
    Returns:
        Result of the operation
        
    Raises:
        DatabaseError: If operation fails after retries
    """
    try:
        logger.debug("Executing database operation with retry logic")
        result = await operation()
        logger.debug("Database operation completed successfully")
        return result
    except Exception as e:
        logger.error(f"Database operation failed after retries: {str(e)}")
        raise DatabaseErrorHandler.classify_database_error(e)


class DatabaseHealthChecker:
    """Database health monitoring and connection validation"""
    
    @staticmethod
    async def check_connection_health(session: AsyncSession) -> bool:
        """
        Check if database connection is healthy
        
        Args:
            session: Database session to check
            
        Returns:
            bool: True if connection is healthy, False otherwise
        """
        try:
            # Simple query to test connection
            await session.execute("SELECT 1")
            return True
        except Exception as e:
            logger.warning(f"Database health check failed: {str(e)}")
            return False
    
    @staticmethod
    async def validate_session(session: AsyncSession) -> AsyncSession:
        """
        Validate database session and recreate if necessary
        
        Args:
            session: Database session to validate
            
        Returns:
            AsyncSession: Valid database session
            
        Raises:
            DatabaseError: If session cannot be validated or recreated
        """
        try:
            if await DatabaseHealthChecker.check_connection_health(session):
                return session
            else:
                logger.warning("Database session is unhealthy, attempting to recreate")
                # Note: In practice, you would recreate the session here
                # This depends on your specific database setup
                raise DatabaseError(
                    message="Database session is unhealthy and cannot be recreated",
                    operation="session_validation",
                    code=ErrorCode.DATABASE_CONNECTION_ERROR,
                    severity=ErrorSeverity.CRITICAL
                )
        except Exception as e:
            raise DatabaseErrorHandler.classify_database_error(e)


def with_database_error_handling(operation: str = "database_operation"):
    """
    Comprehensive decorator combining error handling, retries, and health checks
    
    Args:
        operation: Description of the database operation
    """
    def decorator(func):
        @functools.wraps(func)
        @handle_database_errors(operation)
        async def wrapper(*args, **kwargs):
            # Extract session from args/kwargs if present
            session = None
            for arg in args:
                if isinstance(arg, AsyncSession):
                    session = arg
                    break
            
            if not session and "session" in kwargs:
                session = kwargs["session"]
            
            if session:
                # Validate session health before operation
                session = await DatabaseHealthChecker.validate_session(session)
            
            # Execute with retry logic
            async def operation_func():
                return await func(*args, **kwargs)
            
            if session:
                return await execute_with_retry(session, operation_func)
            else:
                return await operation_func()
        
        return wrapper
    
    return decorator


# Utility functions for common database operations
async def safe_query_one(session: AsyncSession, query, params=None):
    """
    Safely execute a query that should return one result
    
    Args:
        session: Database session
        query: SQL query to execute
        params: Query parameters
        
    Returns:
        Query result or None
        
    Raises:
        DatabaseError: If query fails
    """
    try:
        result = await session.execute(query, params)
        return result.fetchone()
    except Exception as e:
        raise DatabaseErrorHandler.classify_database_error(e)


async def safe_query_all(session: AsyncSession, query, params=None):
    """
    Safely execute a query that should return multiple results
    
    Args:
        session: Database session
        query: SQL query to execute
        params: Query parameters
        
    Returns:
        List of query results
        
    Raises:
        DatabaseError: If query fails
    """
    try:
        result = await session.execute(query, params)
        return result.fetchall()
    except Exception as e:
        raise DatabaseErrorHandler.classify_database_error(e)
