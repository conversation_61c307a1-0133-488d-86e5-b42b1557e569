import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from langchain.prompts import PromptTemplate
from langchain.chains import LLMChain
from langchain.tools import Tool
from langchain.agents import initialize_agent, AgentType
import random

from sqlalchemy.ext.asyncio import AsyncSession

from app.schemas.watchlist_schemas import WatchlistSchema
from app.utils import cache
from app.utils.llm_utils import get_openai_model_v3


class WatchlistLangChainStockDataGenerator:
    def __init__(self):
        """
        Initialize Pure LangChain Stock Generator - NO external APIs
        Only uses OpenAI through LangChain for all data generation
        """
        self.llm = get_openai_model_v3()
        self.setup_prompts()
        self.setup_chains()
        self.setup_tools()
        self.setup_agent()

    def setup_prompts(self):
        """
        Setup prompts based on exact image analysis
        Image shows: Ticker, Company name, Exchange, Sector/Industry, Price, Change, Pre-market data, AI analysis
        """

        # Main stock data generation prompt
        self.main_stock_prompt = PromptTemplate(
            input_variables=["ticker"],
            template="""
            Generate realistic stock market data for ticker symbol: {ticker}

            Analyze this ticker and provide comprehensive stock information in this EXACT JSON format:
            {{
                "ticker": "{ticker}",
                "company_name": "[Full company name for this ticker]",
                "exchange": "[NASDAQ/NYSE/etc - determine appropriate exchange]",
                "sector": "[Business sector like Consumer Durables, Technology, etc]",
                "industry": "[Specific industry like Motor Vehicles, Software, etc]",
                "current_price": [realistic stock price in USD],
                "previous_close": [yesterday's closing price],
                "change_amount": [price change in dollars],
                "change_percent": [percentage change with 2 decimals],
                "last_updated_time": "01:59 GMT+2",
                "currency": "USD"
            }}

            Requirements:
            - Research the actual company behind this ticker symbol
            - Generate realistic price ranges appropriate for this company
            - Calculate proper change amounts and percentages
            - Use appropriate exchange (NASDAQ for tech, NYSE for traditional companies)
            - Ensure sector/industry alignment with the actual business
            - Price should be realistic for current market conditions

            Return ONLY the JSON object, no additional text.
            """
        )

        # Pre-market data generation prompt
        self.premarket_prompt = PromptTemplate(
            input_variables=["ticker", "current_price", "change_percent", "sector"],
            template="""
            Generate realistic pre-market trading data for {ticker}:

            Context:
            - Current Price: ${current_price}
            - Regular Hours Change: {change_percent}%
            - Sector: {sector}

            Generate pre-market data in this EXACT JSON format:
            {{
                "premarket_price": [realistic pre-market price],
                "premarket_change": [change from previous close],
                "premarket_change_percent": [percentage change with 2 decimals],
                "premarket_time": "13:49 GMT+2",
                "premarket_indicator": "Pre-market"
            }}

            Guidelines:
            - Pre-market price should be within -3% to +5% of current price
            - Consider extending regular hours trend or creating slight reversal
            - Pre-market changes are typically smaller but can be more volatile
            - Ensure mathematical accuracy in calculations

            Return ONLY the JSON object, no additional text.
            """
        )

        # AI analysis generation prompt (matching image style)
        self.analysis_prompt = PromptTemplate(
            input_variables=["ticker", "company_name", "current_price", "change_percent",
                             "sector", "premarket_change"],
            template="""
            Generate a professional AI market analysis for {company_name} ({ticker}) in the style shown in financial apps:

            Current Market Data:
            - Price: ${current_price}
            - Change: {change_percent}%
            - Sector: {sector}
            - Pre-market movement: {premarket_change}%

            Write a concise analysis (3-4 sentences) covering:
            1. Current market sentiment and price momentum
            2. Key factors driving today's performance
            3. Sector context and competitive positioning
            4. Brief outlook or technical perspective

            Style requirements:
            - Professional financial tone
            - Mention specific data points (NFP, earnings, sector trends)
            - Include market sentiment terminology
            - Reference broader market context
            - Keep it informative but accessible

            Example style: "Today's top gainers saw strong upward momentum across the board following this morning's highly anticipated Non-Farm Payrolls (NFP) release, which beat expectations and triggered bullish sentiment across major sectors. The data painted a robust labor market picture, sparking optimism about continued economic resilience..."

            Generate similar analysis for {ticker} based on its current performance.

            **note**:
            - Always respond with pure html format using html tags like <p>, <ul>, <li>. response should be directly rendered in UI with innerHTML.
            """
        )

        # Company research prompt for accurate information
        self.company_research_prompt = PromptTemplate(
            input_variables=["ticker"],
            template="""
            Research and provide accurate information about the company with ticker symbol {ticker}:

            Provide detailed company information in JSON format:
            {{
                "official_name": "[Official full company name]",
                "business_description": "[What the company does - 1 sentence]",
                "primary_exchange": "[Main trading exchange]",
                "sector_classification": "[Primary business sector]",
                "industry_category": "[Specific industry]",
                "market_tier": "[large-cap/mid-cap/small-cap based on typical market cap]",
                "business_model": "[Brief description of how they make money]",
                "key_products": "[Main products or services]",
                "geographic_focus": "[Primary markets - US, Global, etc]"
            }}

            Research this ticker thoroughly to provide accurate, real-world information.
            Do not make up fake companies - use your knowledge of actual public companies.

            Return ONLY the JSON object.
            """
        )

    def setup_chains(self):
        """
        Setup LangChain chains for different data generation tasks
        """
        self.main_stock_chain = LLMChain(llm=self.llm, prompt=self.main_stock_prompt)
        self.premarket_chain = LLMChain(llm=self.llm, prompt=self.premarket_prompt)
        self.analysis_chain = LLMChain(llm=self.llm, prompt=self.analysis_prompt)
        self.research_chain = LLMChain(llm=self.llm, prompt=self.company_research_prompt)

    def generate_stock_data(self, ticker: str) -> Dict:
        """
        Generate complete stock data using only OpenAI through LangChain
        """
        try:
            # First, research the company for accurate information
            print(f"Researching company information for {ticker}...")
            research_result = self.research_chain.run(ticker=ticker.upper())
            print(research_result)
            research_result = research_result.replace("json", "").replace("\n", "").replace("html", "").replace("`", "")
            company_info = json.loads(research_result.strip())

            # Generate main stock data
            print(f"Generating market data for {ticker}...")
            stock_result = self.main_stock_chain.run(ticker=ticker.upper())
            stock_result = stock_result.replace("json", "").replace("\n", "").replace("html", "").replace("`", "")
            stock_data = json.loads(stock_result.strip())

            # Enhance with research data
            stock_data.update({
                "company_name": company_info.get("official_name", stock_data["company_name"]),
                "sector": company_info.get("sector_classification", stock_data["sector"]),
                "industry": company_info.get("industry_category", stock_data["industry"]),
                "exchange": company_info.get("primary_exchange", stock_data["exchange"]),
                "business_description": company_info.get("business_description", ""),
                "market_tier": company_info.get("market_tier", "large-cap")
            })

            return stock_data

        except Exception as e:
            print(f"Error in main data generation: {e}")
            # Fallback generation
            return self.generate_fallback_data(ticker)

    def generate_premarket_data(self, ticker: str, stock_data: Dict) -> Dict:
        """
        Generate pre-market data using OpenAI
        """
        try:
            print(f"Generating pre-market data for {ticker}...")
            premarket_result = self.premarket_chain.run(
                ticker=ticker,
                current_price=stock_data["current_price"],
                change_percent=stock_data["change_percent"],
                sector=stock_data["sector"]
            )
            premarket_result = premarket_result.replace("json", "").replace("\n", "").replace("html", "").replace("`",
                                                                                                                  "")
            premarket_data = json.loads(premarket_result.strip())
            return premarket_data

        except Exception as e:
            print(f"Error generating pre-market data: {e}")
            # Fallback pre-market data
            current_price = stock_data["current_price"]
            premarket_change_percent = random.uniform(-2, 3)
            premarket_price = current_price * (1 + premarket_change_percent / 100)
            premarket_change = premarket_price - stock_data["previous_close"]

            return {
                "premarket_price": round(premarket_price, 2),
                "premarket_change": round(premarket_change, 2),
                "premarket_change_percent": round(premarket_change_percent, 2),
                "premarket_time": "13:49 GMT+2",
                "premarket_indicator": "Pre-market"
            }

    def generate_ai_analysis(self, ticker: str, stock_data: Dict, premarket_data: Dict) -> str:
        """
        Generate AI market analysis matching the image style
        """
        try:
            print(f"Generating AI analysis for {ticker}...")
            analysis = self.analysis_chain.run(
                ticker=ticker,
                company_name=stock_data["company_name"],
                current_price=stock_data["current_price"],
                change_percent=stock_data["change_percent"],
                sector=stock_data["sector"],
                premarket_change=premarket_data["premarket_change_percent"]
            )
            return analysis.strip()

        except Exception as e:
            print(f"Error generating analysis: {e}")
            # Fallback analysis
            sentiment = "bullish" if stock_data["change_percent"] > 0 else "bearish"
            return f"Market sentiment remains {sentiment} for {stock_data['company_name']} following recent trading activity. The {stock_data['sector']} sector continues to show mixed signals as investors weigh current market conditions. Technical indicators suggest continued volatility in the near term with key support levels being closely monitored."

    def generate_fallback_data(self, ticker: str) -> Dict:
        """
        Generate basic fallback data if all other methods fail
        """
        # Simple fallback with reasonable defaults
        base_price = random.uniform(50, 300)
        change_percent = random.uniform(-5, 5)
        change_amount = base_price * (change_percent / 100)
        previous_close = base_price - change_amount

        return {
            "ticker": ticker.upper(),
            "company_name": f"{ticker.upper()} Corporation",
            "exchange": "NASDAQ",
            "sector": "Technology",
            "industry": "Software",
            "current_price": round(base_price, 2),
            "previous_close": round(previous_close, 2),
            "change_amount": round(change_amount, 2),
            "change_percent": round(change_percent, 2),
            "last_updated_time": "01:59 GMT+2",
            "currency": "USD"
        }

    def generate_complete_report(self, ticker: str) -> Dict:
        """
        Generate complete stock report matching the image format exactly
        """
        print(f"\n🤖 Generating complete stock report for {ticker.upper()}...")
        print("=" * 60)

        # Generate main stock data
        stock_data = self.generate_stock_data(ticker)
        print(stock_data)

        # Generate pre-market data
        premarket_data = self.generate_premarket_data(ticker, stock_data)
        print(premarket_data)

        # Generate AI analysis
        ai_analysis = self.generate_ai_analysis(ticker, stock_data, premarket_data)

        # Compile complete report matching image structure
        complete_report = {
            # Main ticker info (top section of image)
            "ticker": stock_data["ticker"],
            "company_name": stock_data["company_name"],
            "exchange": stock_data["exchange"],
            "sector": stock_data["sector"],
            "industry": stock_data["industry"],

            # Price data (main price section)
            "current_price": float(stock_data["current_price"]),
            "currency": stock_data["currency"],
            "change_amount": float(stock_data["change_amount"]),
            "change_percent": float(stock_data["change_percent"]),
            "last_updated": stock_data["last_updated_time"],

            # Pre-market section
            "premarket": {
                "price": float(premarket_data["premarket_price"]),
                "change": float(premarket_data["premarket_change"]),
                "change_percent": float(premarket_data["premarket_change_percent"]),
                "indicator": premarket_data["premarket_indicator"],
                "last_updated": premarket_data["premarket_time"]
            },

            # AI analysis section
            "ai_analysis": {
                "provider": "SAN.MC.AI",
                "content": ai_analysis
            },

            # Generation metadata
            "generated_at": datetime.now().isoformat(),
            "data_source": "langchain_openai_only"
        }

        return complete_report

    def format_display_output(self, report: Dict) -> str | dict[str, dict[str, str] | str | dict[str, str] | Any]:
        """
        Format the report for display similar to the UI
        """
        # Determine color coding for changes
        print(report)
        change_sign = "+" if report["change_amount"] >= 0 else ""
        change_percent_sign = "+" if report["change_percent"] >= 0 else ""
        premarket_change_sign = "+" if report["premarket"]["change"] >= 0 else ""
        premarket_percent_sign = "+" if report["premarket"]["change_percent"] >= 0 else ""

        mapping_dict = {
            "ticker": report["ticker"],
            "title": f'{report["company_name"]} • {report["exchange"]}',
            "subtitle": f'{report["sector"]} • {report["industry"]}',
            "market_data": {
                "price": f'${report["current_price"]} USD',
                "price_version": f'{change_sign}{report["change_amount"]} {change_percent_sign}{report["change_percent"]}%',
                "date": f'Last updated at {report["last_updated"]}'
            },
            "pre_market": {
                "price": f'{report["premarket"]["price"]} {report["currency"]}',
                "price_version": f'{premarket_change_sign}{report["premarket"]["change"]} {premarket_percent_sign}{report["premarket"]["change_percent"]}%',
                "date": f'Last update at {report["premarket"]["last_updated"]}'
            },
            "analysis": report["ai_analysis"]["content"]
        }

        return mapping_dict

    def setup_tools(self):
        """
        Setup LangChain tools for agent usage
        """
        self.stock_tool = Tool(
            name="StockDataGenerator",
            description="Generate comprehensive stock market data for any ticker symbol using only AI",
            func=lambda ticker: json.dumps(self.generate_complete_report(ticker))
        )

    def setup_agent(self):
        """
        Setup LangChain agent for natural language queries
        """
        tools = [self.stock_tool]
        self.agent = initialize_agent(
            tools,
            self.llm,
            agent=AgentType.ZERO_SHOT_REACT_DESCRIPTION,
            verbose=True
        )

    def process_natural_query(self, query: str) -> str:
        """
        Process natural language stock queries using the agent
        """
        try:
            response = self.agent.run(query)
            return response
        except Exception as e:
            return f"Query processing error: {str(e)}"

async def generate_data_for_ticker(ticker_name: str):
    try:
        generator = WatchlistLangChainStockDataGenerator()
        report = generator.generate_complete_report(ticker_name)
        mapping_report = generator.format_display_output(report)
        return mapping_report

    except Exception as e:
        return {}

async def getting_watchlist_data_for_ticker(
        db: AsyncSession,
        ticker,
):
    try:
        watchlist_data = cache.get("stock_watchlist_analysis") or {}
        ticker_name = ticker
        if watchlist_data:
            try:
                get_ticker_data = watchlist_data[ticker_name]
                if get_ticker_data:
                    response = get_ticker_data
                else:
                    response = await generate_data_for_ticker(ticker_name)
            except:
                response = await generate_data_for_ticker(ticker_name)
        else:
            response = await generate_data_for_ticker(ticker_name)

        return response
    except Exception as e:
        print(e)
