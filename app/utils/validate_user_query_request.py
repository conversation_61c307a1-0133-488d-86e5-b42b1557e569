import re
from enum import Enum
from typing import Dict, List, Optional

from langchain.chains import <PERSON><PERSON>hai<PERSON>
from langchain.llms import OpenAI
from langchain.memory import ConversationBufferMemory
from langchain.prompts import PromptTemplate
from langchain.schema import BaseOutputParser
from langchain.schema.runnable import Run<PERSON>blePassthrough
from langchain_community.chat_message_histories import Chat<PERSON><PERSON>age<PERSON><PERSON>ory

from app.utils import cache
from app.utils.llm_utils import get_openai_model_v3


class ResponseType(Enum):
    GREETING = "greeting"
    MISSING_INFO = "missing_info"
    EXTERNAL = "external"
    FALLBACK = "fallback"


class FinancialChatbotParser(BaseOutputParser):
    """Custom parser for financial chatbot responses"""

    def parse(self, text: str) -> Dict:
        text = text.strip()

        # Check for external routing
        if "<p>external</p>" in text.lower():
            return {"type": ResponseType.EXTERNAL, "content": "<p>external</p>"}

        # Check for HTML content
        if text.startswith("<") and text.endswith(">"):
            return {"type": ResponseType.MISSING_INFO, "content": text}

        # Default fallback
        return {"type": ResponseType.FALLBACK, "content": f"<p>{text}</p>"}


class FinancialIntentClassifier:
    """Classifies user intents for financial queries"""

    # Simple greeting patterns
    GREETING_PATTERNS = [
        r"\b(hi|hello|hey|good morning|good afternoon|good evening|thanks|thank you|bye|goodbye)\b",
        r"\bhow are you\b",
        r"^\s*(hi|hello|hey)\s*$",
    ]

    # Market news/real-time data patterns
    NEWS_PATTERNS = [
        r"\b(latest|current|today|now|recent|breaking)\b.*\b(news|market|price|trend|analysis)\b",
        r"\bmarket.*\b(today|now|current|doing)\b",
        r"\b(crypto|stock|forex|commodity).*\b(news|update|analysis)\b",
        r"\bwhat.*happening.*\b(market|crypto|stock|forex)\b",
    ]

    # Financial assets/instruments
    FINANCIAL_ASSETS = [
        r"\b(stock|stocks|equity|equities|share|shares)\b",
        r"\b(crypto|cryptocurrency|bitcoin|ethereum|btc|eth)\b",
        r"\b(forex|currency|usd|eur|gbp|jpy)\b",
        r"\b(commodity|commodities|gold|silver|oil)\b",
        r"\b(bond|bonds|treasury|fixed income)\b",
        r"\b(option|options|future|futures|derivative)\b",
        r"\b(apple|aapl|tesla|tsla|google|googl|amazon|amzn|microsoft|msft)\b",  # Popular stocks
    ]

    # Financial actions
    FINANCIAL_ACTIONS = [
        r"\b(buy|sell|trade|invest|hold|exit|enter)\b",
        r"\b(portfolio|investment|position|strategy|gainers|losers|top|bottom|most|least)\b",
        r"\b(analysis|forecast|prediction|signal|trend)\b",
        r"\b(risk|return|profit|loss|volatility)\b",
    ]

    def classify_intent(self, text: str, conversation_history: str = "") -> str:
        text_lower = text.lower()
        full_context = f"{conversation_history} {text}".lower()

        # Check for greetings
        if any(re.search(pattern, text_lower) for pattern in self.GREETING_PATTERNS):
            return "greeting"

        # Check for market news requests
        if any(re.search(pattern, text_lower) for pattern in self.NEWS_PATTERNS):
            return "market_news"

        # Check if it's a financial query with sufficient context
        has_asset = any(
            re.search(pattern, full_context) for pattern in self.FINANCIAL_ASSETS
        )
        has_action = any(
            re.search(pattern, full_context) for pattern in self.FINANCIAL_ACTIONS
        )

        if has_asset and has_action:
            return "complete_financial"
        elif has_asset or has_action:
            return "partial_financial"
        else:
            return "unclear"


class FinancialChatbot:
    """Main Financial Chatbot class using LangChain"""

    def __init__(self, llm_model="gpt-3.5-turbo"):
        self.classifier = FinancialIntentClassifier()
        self.parser = FinancialChatbotParser()

        # Initialize LangChain components
        self.llm = get_openai_model_v3()

        # Conversation memory (keep last 4 exchanges)
        self.memory = None

        # Define prompts for different scenarios
        self.setup_prompts()
        self.setup_chains()

    def create_memory_from_manual_history_dict(self, conversation_history):
        """
        Create ConversationBufferMemory from manually managed conversation history

        Args:
            conversation_history (list): List of dictionaries with 'role' and 'content' keys
                                       Example: [
                                           {'role': 'user', 'content': 'Hello'},
                                           {'role': 'assistant', 'content': 'Hi there!'},
                                           {'role': 'user', 'content': 'What is AAPL price?'}
                                       ]

        Returns:
            ConversationBufferMemory: Memory object with conversation history
        """
        # Create a basic ChatMessageHistory
        message_history = ChatMessageHistory()
        # Add messages from your manual history
        for history in conversation_history[:-3:-1]:
            message_history.add_user_message(history[0])
            message_history.add_ai_message(history[1])

        # Create memory with the populated message history
        memory = ConversationBufferMemory(
            k=4,
            memory_key="chat_history",
            return_messages=True,
            chat_memory=message_history,
        )

        self.memory = memory

        return memory

    def setup_prompts(self):
        """Setup different prompt templates"""

        # Greeting prompt
        self.greeting_prompt = PromptTemplate(
            input_variables=["user_input"],
            template="""You are a professional Financial Manager AI assistant. 
            The user has greeted you with: "{user_input}"

            Respond with a concise, professional greeting in HTML format.
            Keep it brief and offer financial assistance.

            Example: <p>Good morning! I'm here to help with your financial questions and market analysis. How can I assist you today?</p>

            Response:""",
        )

        # Missing information prompt
        self.missing_info_prompt = PromptTemplate(
            input_variables=["user_input", "chat_history"],
            template="""You are a professional Financial Manager AI assistant.

            Chat History: {chat_history}
            Current Question: {user_input}

            The user's question is missing critical information needed for financial analysis.

            ONLY ask for the MOST ESSENTIAL missing information (maximum 2-3 items):
            - Asset type (if completely unclear: stocks, crypto, forex, etc.)
            - Specific instrument (if asset type known but instrument unclear)

            DO NOT ask for: investment amount, time horizon, risk tolerance, portfolio details unless absolutely critical.

            Format response in HTML with clear, concise questions.

            Example:
            <div>
              <p>I need a bit more information:</p>
              <ul>
                <li><strong>Which asset</strong>: Are you asking about stocks, crypto, or forex?</li>
              </ul>
            </div>

            Response:""",
        )

        # Fallback prompt
        self.fallback_prompt = PromptTemplate(
            input_variables=["user_input"],
            template="""You are a professional Financial Manager AI assistant.

            The user said: "{user_input}"

            This doesn't seem to be a financial question. Politely redirect them to financial topics.
            Respond in HTML format, keep it brief and professional.

            Example: <p>I specialize in financial analysis and market guidance. Could you ask me about investments, trading, or market analysis?</p>

            Response:""",
        )

    def setup_chains(self):
        """Setup LangChain chains"""
        self.greeting_chain = LLMChain(llm=self.llm, prompt=self.greeting_prompt)
        self.missing_info_chain = LLMChain(
            llm=self.llm, prompt=self.missing_info_prompt
        )
        self.fallback_chain = LLMChain(llm=self.llm, prompt=self.fallback_prompt)

    def get_conversation_context(self) -> str:
        """Get recent conversation history as string"""
        messages = self.memory.chat_memory.messages
        if not messages:
            return ""

        # Get last few messages
        recent_messages = messages[-6:]  # Last 3 exchanges (user + bot)
        context = []

        for msg in recent_messages:
            if hasattr(msg, "content"):
                # Clean HTML tags for context analysis
                clean_content = re.sub(r"<[^>]+>", "", msg.content)
                context.append(clean_content)

        return " ".join(context)

    def generate_realtime_question(self, memory):
        """
        Generate a detailed question based on conversation history

        Returns:
            str: Generated question for real-time data fetching
        """
        try:
            # Get conversation history from memory
            chat_history = memory.chat_memory.messages

            # Format chat history for the prompt
            formatted_history = ""
            for message in chat_history:
                if hasattr(message, "content"):
                    role = "User" if message.type == "human" else "Assistant"
                    formatted_history += f"{role}: {message.content}\n"

            if not formatted_history.strip():
                return ""

            # Question generation prompt for creating detailed queries from conversation context
            question_generator_prompt = """You are a financial query generator. Based on conversation history, create a specific, detailed question that can be used to fetch financial data.

            ### TASK
            Extract key parameters from the conversation history and create a comprehensive question that includes:
            - Specific asset/instrument name
            - Analysis type (trend, signals, entry/exit, market status, etc.)
            - Timeframe (if mentioned)
            - Technical indicators (if specified)
            - Any other relevant context

            ### OUTPUT FORMAT
            Return ONLY the generated question as plain text. Do not include explanations or additional text.

            ### EXAMPLES

            Chat History: 
            User: "Is this a good time to enter?"
            Assistant: "I'd be happy to help with entry timing. Could you clarify: Which asset are you considering? Are you looking to go long or short?"
            User: "AAPL long position"
            User_1: "AAPL or apple"

            Generated Question: "Is this a good time to enter a long position in AAPL?"
            Generated Question: "Is this a good time to enter in AAPL?"

            ---

            Chat History:
            User: "What's the trend right now?"
            Assistant: "To analyze the current trend, I need to know: Which instrument? What timeframe?"
            User: "Bitcoin daily chart"

            Generated Question: "What is the current trend of Bitcoin on the daily chart?"

            ---

            Chat History:
            User: "Show me signals"
            Assistant: "I can help you with trading signals. Please specify: For which asset? What type of signals?"
            User: "TSLA RSI signals"
            User_1: "TSLA or Tesla"

            Generated Question: "Show me RSI trading signals for TSLA"
            Generated Question: "Show me signals for Tesla"

            ---

            ### CONVERSATION HISTORY TO ANALYZE:
            {chat_history}

            Generated Question:"""

            # Create question generator prompt template
            question_gen_template = PromptTemplate(
                input_variables=["chat_history"], template=question_generator_prompt
            )

            # Create the question generator chain (without memory to avoid interference)
            question_gen_chain = LLMChain(
                llm=get_openai_model_v3(), prompt=question_gen_template, verbose=False
            )

            # Generate the question
            generated_question = question_gen_chain.predict(
                chat_history=formatted_history
            )
            return generated_question.strip()

        except Exception as e:
            print(f"Error generating question: {e}")
            return ""

    def process_query(
        self, user_input, conversation_history
    ) -> dict[str, ResponseType | str | bool] | str:
        """Main method to process user queries"""

        # Get conversation context
        memory = self.create_memory_from_manual_history_dict(conversation_history)
        conversation_context = self.get_conversation_context()

        # Classify intent
        intent = self.classifier.classify_intent(user_input, conversation_context)

        try:
            if intent == "greeting":
                response = self.greeting_chain.run(user_input=user_input)
                result = self.parser.parse(response)

            elif intent == "market_news":
                result = {"type": ResponseType.EXTERNAL, "content": "<p>external</p>"}

            elif intent == "complete_financial":
                result = {"type": ResponseType.EXTERNAL, "content": "<p>external</p>"}

            elif intent == "partial_financial":
                response = self.missing_info_chain.run(
                    user_input=user_input, chat_history=conversation_context
                )
                result = self.parser.parse(response)

            else:  # unclear
                response = self.fallback_chain.run(user_input=user_input)
                result = self.parser.parse(response)

            # Store in memory
            self.memory.chat_memory.add_user_message(user_input)
            self.memory.chat_memory.add_ai_message(result["content"])

            result_dict = {"answer": result["content"], "is_external": False}
            if "external" in result["content"]:
                result_dict["is_external"] = True
                new_question = self.generate_realtime_question(self.memory)
                result_dict["new_question"] = new_question
            else:
                result_dict["is_external"] = False
                result_dict["new_question"] = user_input

            return result_dict

        except Exception as e:
            print(f"Error processing query: {e}")
            return {
                "answer": "<p>I apologize, but I'm having trouble processing your request. Please try rephrasing your financial question.</p>",
                "is_external": True,
                "new_question": user_input,
            }


# Usage Example and Testing
def validate_user_question(question, chat_id, conversation_history):
    """Test the financial chatbot with various scenarios"""
    validate_list = cache.get(f"context_validate_mapping_{chat_id}") or []
    chatbot = FinancialChatbot()

    response = chatbot.process_query(question, conversation_history)

    if response["is_external"]:
        validate_list.append("realtime")
    else:
        validate_list.append("not_realtime")

    if len(validate_list)>=2:
        external_required = validate_list[-2] == "not_realtime"
        if external_required:
            memory = chatbot.memory
            new_question = chatbot.generate_realtime_question(memory)
            response["new_question"]=new_question
            response["is_external"]=True
            validate_list[-1]="realtime"

    cache.set_user_cache(f"context_validate_mapping_{chat_id}", validate_list)
    try:
        mapping_dict = {
            "answer": response["answer"],
            "is_realtime": response["is_external"],
            "new_question": response["new_question"],
        }
    except:
        mapping_dict = {
            "answer": "<p>I'm sorry, I encountered an error processing your request. Please try again.</p>",
            "is_realtime": True,
            "new_question": question,
        }

    return mapping_dict
