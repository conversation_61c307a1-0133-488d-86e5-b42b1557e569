import base64
import json
import os
import re
import uuid
from datetime import date, datetime
from decimal import Decimal
from pathlib import Path

import anthropic
from fastapi import UploadFile
from langchain.chains import <PERSON><PERSON><PERSON>n
from langchain.chat_models import ChatOpenAI
from langchain.prompts import Chat<PERSON>romptTemplate
from langchain.schema import AIMessage, HumanMessage, SystemMessage
from langchain.schema.output_parser import StrOutputParser
from langchain.schema.runnable import RunnablePassthrough
from langchain_anthropic import ChatAnthropic
from langchain_openai import ChatOpenAI

# from app.db.sync_db import engine
from sqlalchemy import create_engine, text

from app.core.config import settings
from app.core.llm_config import client
from app.services.web_search_services import web_scrape_search_stream
from app.utils.logger import generic_logger as logger
from app.utils.s3_utils import delete_public_file_from_s3, upload_to_s3_as_public
from app.utils.token_cost_tracker import calculate_tokens_and_cost
from app.utils.comprehensive_cost_tracker import track_openai_response

model_name = settings.MODEL_NAME


def base64url_decode(input):
    input += "=" * (4 - len(input) % 4)
    return base64.urlsafe_b64decode(input)


def decode_jwt(token):
    header, payload, signature = token.split(".")

    decoded_header = base64url_decode(header).decode("utf-8")
    decoded_payload = base64url_decode(payload).decode("utf-8")

    return json.loads(decoded_header), json.loads(decoded_payload)


def classify_or_answer_question(user_question, classify_prompt):  # history,
    """
    Uses OpenAI API to classify the question or generate an answer.
    """
    import time
    start_time = time.time()

    messages = [
        {"role": "system", "content": f"{classify_prompt}"},
        {"role": "user", "content": user_question},
    ]

    response = client.chat.completions.create(
        model=model_name, messages=messages, temperature=0.2, max_tokens=4096, top_p=0.2
    )

    response_result = response.choices[0].message.content

    # Track cost with comprehensive tracker
    duration_ms = (time.time() - start_time) * 1000
    track_openai_response(
        model_name=model_name,
        function_name="classify_or_answer_question",
        messages=messages,
        response=response,
        duration_ms=duration_ms
    )

    # Calculate token usage and costs (keep existing for backward compatibility)
    usage_stats = calculate_tokens_and_cost(
        messages=messages,
        response_tokens=response.usage.completion_tokens,
        model_name=settings.MODEL_NAME,
    )

    print("\nToken Usage and Costs in First prompt analysis============>:")
    print(f"Input Tokens: {usage_stats['input_tokens']}")
    print(f"Output Tokens: {usage_stats['output_tokens']}")
    print(f"Input Cost: ${usage_stats['input_cost']}")
    print(f"Output Cost: ${usage_stats['output_cost']}")
    print(f"Total Cost: ${usage_stats['total_cost']}")

    return response_result


def classify_or_answer_question_v3(user_question, classify_prompt):  # history,
    """
    Uses OpenAI API to classify the question or generate an answer.
    """
    import time
    start_time = time.time()

    llm = ChatOpenAI(
        model_name=model_name,
        temperature=0.2,
        max_tokens=4096,
    )

    prompt = ChatPromptTemplate.from_messages(
        [("system", "{classify_prompt}"), ("human", "{user_question}")]
    )

    chain = LLMChain(llm=llm, prompt=prompt)

    response = chain.run(classify_prompt=classify_prompt, user_question=user_question)

    response_result = response

    # Track cost with comprehensive tracker
    duration_ms = (time.time() - start_time) * 1000
    from app.utils.comprehensive_cost_tracker import cost_tracker
    cost_tracker.track_model_usage(
        model_name=model_name,
        function_name="classify_or_answer_question_v3",
        input_text=f"{classify_prompt}\n{user_question}",
        output_text=response,
        duration_ms=duration_ms,
        actual_output_tokens=llm.get_num_tokens(response)
    )

    # Calculate token usage and costs (keep existing for backward compatibility)
    usage_stats = calculate_tokens_and_cost(
        messages=[
            {"role": "system", "content": f"{classify_prompt}"},
            {"role": "user", "content": user_question},
        ],
        response_tokens=llm.get_num_tokens(response),
        model_name=settings.MODEL_NAME,
    )

    print("\nToken Usage and Costs in First prompt analysis============>:")
    print(f"Input Tokens: {usage_stats['input_tokens']}")
    print(f"Output Tokens: {usage_stats['output_tokens']}")
    print(f"Input Cost: ${usage_stats['input_cost']}")
    print(f"Output Cost: ${usage_stats['output_cost']}")
    print(f"Total Cost: ${usage_stats['total_cost']}")

    return response_result


def trade_or_answer_question(user_question, trade_prompt):  # history,
    """
    Uses OpenAI API to classify the question or generate an answer.
    """
    import time
    start_time = time.time()

    messages = [
        {"role": "system", "content": f"{trade_prompt}"},
        {"role": "user", "content": user_question},
    ]

    response = client.chat.completions.create(
        model=model_name, messages=messages, temperature=0.2, max_tokens=4096, top_p=0.2
    )

    response_result = response.choices[0].message.content

    # Track cost with comprehensive tracker
    duration_ms = (time.time() - start_time) * 1000
    track_openai_response(
        model_name=model_name,
        function_name="trade_or_answer_question",
        messages=messages,
        response=response,
        duration_ms=duration_ms
    )

    # Calculate token usage and costs (keep existing for backward compatibility)
    usage_stats = calculate_tokens_and_cost(
        messages=messages,
        response_tokens=response.usage.completion_tokens,
        model_name=settings.MODEL_NAME,
    )

    print("\nToken Usage and Costs in Trade prompt analysis============>:")
    print(f"Input Tokens: {usage_stats['input_tokens']}")
    print(f"Output Tokens: {usage_stats['output_tokens']}")
    print(f"Input Cost: ${usage_stats['input_cost']}")
    print(f"Output Cost: ${usage_stats['output_cost']}")
    print(f"Total Cost: ${usage_stats['total_cost']}")

    return response_result


def trade_or_answer_question_v3(user_question, trade_prompt):  # history,
    """
    Uses OpenAI API to classify the question or generate an answer.
    """
    import time
    start_time = time.time()

    llm = ChatOpenAI(
        model_name=model_name,
        temperature=0.2,
        max_tokens=4096,
    )

    prompt = ChatPromptTemplate.from_messages(
        [("system", "{trade_prompt}"), ("human", "{user_question}")]
    )

    chain = LLMChain(llm=llm, prompt=prompt)

    response = chain.run(trade_prompt=trade_prompt, user_question=user_question)

    response_result = response

    # Track cost with comprehensive tracker
    duration_ms = (time.time() - start_time) * 1000
    from app.utils.comprehensive_cost_tracker import cost_tracker
    cost_tracker.track_model_usage(
        model_name=model_name,
        function_name="trade_or_answer_question_v3",
        input_text=f"{trade_prompt}\n{user_question}",
        output_text=response,
        duration_ms=duration_ms,
        actual_output_tokens=llm.get_num_tokens(response)
    )

    # Calculate token usage and costs (keep existing for backward compatibility)
    usage_stats = calculate_tokens_and_cost(
        messages=[
            {"role": "system", "content": f"{trade_prompt}"},
            {"role": "user", "content": user_question},
        ],
        response_tokens=llm.get_num_tokens(response),
        model_name=settings.MODEL_NAME,
    )

    print("\nToken Usage and Costs in Trade prompt analysis============>:")
    print(f"Input Tokens: {usage_stats['input_tokens']}")
    print(f"Output Tokens: {usage_stats['output_tokens']}")
    print(f"Input Cost: ${usage_stats['input_cost']}")
    print(f"Output Cost: ${usage_stats['output_cost']}")
    print(f"Total Cost: ${usage_stats['total_cost']}")

    return response_result


# Testing history ++++++++++++++++++++++++++++


def check_question_from_history(user_question, history):
    """
    Uses OpenAI API to classify the question or generate an answer.
    """

    classify_prompt = """You are vuetra's ai chat analysis expert.You have multiple tasks to do.

        ## Your primary responsibility is to efficiently analyse user question in which steps we need to answer. and answer the user question with HTML-rich text responses. The task is segmented into three distinct steps to ensure comprehensive analysis.


        ### **Step 1: Your task is to analyse if you have a response for the users question from the users previous conversation or question from history what we are passing.

        - If you have a response for the users question from the previous conversation, use that conversation to answer the users question.
        - If you dont have a response from the previous conversation, analyze the users new question whether it is related to the previous conversation or not.
        - If it is related to the previous conversation, and we don't have a response in that previous conversation, generate a new question by analyzing the users new question and previous conversation.


        ### **Step 2: Initial Query Analysis**
        - **If user have general questions, respond using your knowledge base in detail with proper para and bullet points with explanations.**

        #### **Scenario Responses for Specific Types of Questions:**

        1. **Technical Support Issues:**
        - User: "The chart isn't loading properly on my trading platform."
        - User: "I can't place trades, the platform is unresponsive."

        2. **Legal or Compliance Questions (Use the Provided Paragraph):**
        - User: "Can you explain the terms and conditions for using the platform?"
        - User: "What are the platform's policies on data protection?"
        - User: "How do you handle user data according to privacy laws?"
        - **Paragraph:** Our platform is committed to maintaining the highest standards of legal and compliance practices...

        3. **Platform Feedback or Complaints:**
        - User: "The new update has made the platform harder to use."
        - User: "The platform is very slow during peak hours."
        - User: "I don't like the recent changes to the charting tools."

        4. **Requests Involving Personal Data:**
        - User: "Can I see the trading data of another user?"
        - User: "Can you show me the recent trades of another user?"
        - User: "Can you provide the account balance of another trader?"
        - **Action:** Politely inform the user that you cannot provide such information.

        5. **Data Modification Requests:**
        - User: "delete the data?"
        - User: "drop the data?"
        - User: "remove the tables or anything?"
        - **Action:** Politely inform the user that you cannot entertain such requests.

        #example questions
        1. All greeting type questions.
        2. All general questions.
        3. All theoritical forex or trading related questions.


        ### **Step 3: If external data is required, to answer the user's question**
        - **Assess whether the user's question requires external data to provide an answer, is user's question need external data to answer.**

        #example questions
        1. What all trades i have done?
        2. What is my account balance?
        3. What is latest news?
        4. Give me some insider news about company?
        5. Need information about news, events, or any data

        {
            "response": "<Provide response if you have a response, otherwise 'Null'>",
            "is_new_question": "<Whether the new question we have by analyzing the users new question and previous conversation or question, otherwise 'Null'>",
            "answer": "<p>Provide model's answer must be in a <strong>HTML rich text with para and bullet points if needed</strong> format, otherwise 'Null'.</p>",
            "external_answer": "External, otherwise 'Null'"
        }

        ## NOTE ##
        - Analayse double all the steps then double cross check in which step question is best fit then accordingly respond to user question.
        - At a time once single key will have a response other will be null.
    """

    messages = [{"role": "system", "content": f"{classify_prompt}"}]

    for message in history:
        user_content = message[0] if isinstance(message[0], str) else str(message[0])
        assistant_content = message[1]

        # Ensure assistant content is always a string
        if isinstance(assistant_content, dict):
            assistant_content = assistant_content.get(
                "response", ""
            )  # Extract the actual response
        elif isinstance(assistant_content, list):
            assistant_content = " ".join(
                [str(item) for item in assistant_content]
            )  # Convert list to string
        else:
            assistant_content = str(assistant_content)  # Convert to string

        messages.append({"role": "user", "content": user_content})
        messages.append({"role": "assistant", "content": assistant_content})

    messages.append({"role": "user", "content": user_question})

    response = client.chat.completions.create(
        model=settings.MODEL_NAME,
        messages=messages,
        temperature=0.2,
        max_tokens=4096,
        top_p=0.2,
    )

    # response_result = response.choices[0].message.content
    # response_result = response_result.replace('```json', '').replace('```', '')
    # print("response_result=======================>", response_result)
    # return response_result
    response_result = response.choices[0].message.content
    response_result = response_result.replace("```json", "").replace("```", "")

    # Calculate token usage and costs
    usage_stats = calculate_tokens_and_cost(
        messages=messages,
        response_tokens=response.usage.completion_tokens,
        model_name=settings.MODEL_NAME,
    )

    print("\nToken Usage and Costs in HISTORY analysis============>:")
    print(f"Input Tokens: {usage_stats['input_tokens']}")
    print(f"Output Tokens: {usage_stats['output_tokens']}")
    print(f"Input Cost: ${usage_stats['input_cost']}")
    print(f"Output Cost: ${usage_stats['output_cost']}")
    print(f"Total Cost: ${usage_stats['total_cost']}")

    print("\nresponse_result=======================>", response_result)
    return response_result


def check_question_from_history_v3(user_question, history):
    """
    Uses OpenAI API to classify the question or generate an answer.
    """
    import time
    start_time = time.time()

    try:
        classify_prompt = """You are vuetra's ai chat analysis expert.You have multiple tasks to do.
    
            ## Your primary responsibility is to efficiently analyse user question in which steps we need to answer. and answer the user question with HTML-rich text responses. The task is segmented into three distinct steps to ensure comprehensive analysis.
    
    
            ### **Step 1: Your task is to analyse if you have a response for the users question from the users previous conversation or question from history what we are passing.
    
            - If you have a response for the users question from the previous conversation, use that conversation to answer the users question.
            - If you dont have a response from the previous conversation, analyze the users new question whether it is related to the previous conversation or not.
            - If it is related to the previous conversation, and we don't have a response in that previous conversation, generate a new question by analyzing the users new question and previous conversation.
    
    
            ### **Step 2: Initial Query Analysis**
            - **If user have general questions, respond using your knowledge base in detail with proper para and bullet points with explanations.**
    
            #### **Scenario Responses for Specific Types of Questions:**
    
            1. **Technical Support Issues:**
            - User: "The chart isn't loading properly on my trading platform."
            - User: "I can't place trades, the platform is unresponsive."
    
            2. **Legal or Compliance Questions (Use the Provided Paragraph):**
            - User: "Can you explain the terms and conditions for using the platform?"
            - User: "What are the platform's policies on data protection?"
            - User: "How do you handle user data according to privacy laws?"
            - **Paragraph:** Our platform is committed to maintaining the highest standards of legal and compliance practices...
    
            3. **Platform Feedback or Complaints:**
            - User: "The new update has made the platform harder to use."
            - User: "The platform is very slow during peak hours."
            - User: "I don't like the recent changes to the charting tools."
    
            4. **Requests Involving Personal Data:**
            - User: "Can I see the trading data of another user?"
            - User: "Can you show me the recent trades of another user?"
            - User: "Can you provide the account balance of another trader?"
            - **Action:** Politely inform the user that you cannot provide such information.
    
            5. **Data Modification Requests:**
            - User: "delete the data?"
            - User: "drop the data?"
            - User: "remove the tables or anything?"
            - **Action:** Politely inform the user that you cannot entertain such requests.
    
            #example questions
            1. All greeting type questions.
            2. All general questions.
            3. All theoritical forex or trading related questions.
    
    
            ### **Step 3: If external data is required, to answer the user's question**
            - **Assess whether the user's question requires external data to provide an answer, is user's question need external data to answer.**
    
            #example questions
            1. What all trades i have done?
            2. What is my account balance?
            3. What is latest news?
            4. Give me some insider news about company?
            5. Need information about news, events, or any data
    
            {
                "response": "<Provide response if you have a response, otherwise 'Null'>",
                "is_new_question": "<Whether the new question we have by analyzing the users new question and previous conversation or question, otherwise 'Null'>",
                "answer": "<p>Provide model's answer must be in a <strong>HTML rich text with para and bullet points if needed</strong> format, otherwise 'Null'.</p>",
                "external_answer": "External, otherwise 'Null'"
            }
    
            ## NOTE ##
            - Analayse double all the steps then double cross check in which step question is best fit then accordingly respond to user question.
            - At a time once single key will have a response other will be null.
        """

        # Build messages list
        messages = [SystemMessage(content=classify_prompt)]

        for message in history:
            user_content = (
                message[0] if isinstance(message[0], str) else str(message[0])
            )
            assistant_content = message[1]

            # Ensure assistant content is always a string
            if isinstance(assistant_content, dict):
                assistant_content = assistant_content.get(
                    "response", ""
                )  # Extract the actual response
            elif isinstance(assistant_content, list):
                assistant_content = " ".join(
                    [str(item) for item in assistant_content]
                )  # Convert list to string
            else:
                assistant_content = str(assistant_content)  # Convert to string

            messages.append(HumanMessage(content=user_content))
            messages.append(AIMessage(content=assistant_content))

        messages.append(HumanMessage(content=user_question))

        # Create LangChain components
        llm = ChatOpenAI(
            model=settings.MODEL_NAME,
            temperature=0.2,
            max_tokens=4096,
        )

        # Create chain
        chain = RunnablePassthrough() | llm | StrOutputParser()

        # Execute chain
        response = chain.invoke(messages)
        print(f"response was: {response}")

        response_result = response.replace("```json", "").replace("```", "")
        print(response_result)

        # Track cost with comprehensive tracker
        duration_ms = (time.time() - start_time) * 1000
        from app.utils.comprehensive_cost_tracker import cost_tracker
        input_text = " ".join([msg.content if hasattr(msg, 'content') else str(msg) for msg in messages])
        cost_tracker.track_model_usage(
            model_name=settings.MODEL_NAME,
            function_name="check_question_from_history_v3",
            input_text=input_text,
            output_text=response,
            duration_ms=duration_ms
        )

        # Calculate token usage and costs
        # usage_stats = calculate_tokens_and_cost(
        #     messages=[msg.content if hasattr(msg, 'content') else str(msg) for msg in messages],
        #     response_tokens=len(response.split()),  # Approximate token count
        #     model_name=settings.MODEL_NAME,
        # )
        # print(f"output was: {usage_stats}")
        # print("\nToken Usage and Costs in HISTORY analysis============>:")
        # print(f"Input Tokens: {usage_stats['input_tokens']}")
        # print(f"Output Tokens: {usage_stats['output_tokens']}")
        # print(f"Input Cost: ${usage_stats['input_cost']}")
        # print(f"Output Cost: ${usage_stats['output_cost']}")
        # print(f"Total Cost: ${usage_stats['total_cost']}")

        print("\nresponse_result=======================>", response_result)
        return response_result

    except Exception as e:
        print(f"error coming: {e}")


def query_evaluator(User_question, json_prompt, query_evaluator_prompt):
    messages = [
        {"role": "system", "content": f"{query_evaluator_prompt}"},
        {
            "role": "user",
            "content": [
                {"type": "text", "text": User_question},
                {"type": "text", "text": json.dumps(json_prompt)},
            ],
        },
    ]
    response = client.chat.completions.create(
        model=settings.MODEL_NAME,
        messages=messages,
        temperature=0.2,
        max_tokens=4096,
        top_p=0.2,
        frequency_penalty=0,
        presence_penalty=0,
    )

    response_result = response.choices[0].message.content
    return response_result


from sqlalchemy import create_engine, text


def metric_execute_query(query, uri, parameters=None):
    """Execute a parameterized SQL query safely and log correctly."""
    if not uri:
        print("Database URI is None or invalid: {uri}")
        raise ValueError("Database URI is None or invalid")

    # Create a string representation of the query with actual values for logging
    formatted_query = query
    if parameters:
        for key, value in parameters.items():
            formatted_query = formatted_query.replace(
                f":{key}", f"'{value}'" if isinstance(value, str) else str(value)
            )

    print(f"Executing query: {formatted_query}")

    engine = create_engine(uri)
    with engine.connect() as connection:
        query = text(query)
        results = connection.execute(query, parameters or {})  # Bind parameters safely
        return results.fetchall()


def build_metric_json_data(data):
    json_answer = []
    for row in data:
        row_dict = {}
        for column, value in row._mapping.items():
            if isinstance(value, Decimal):
                value = float(value)
            elif isinstance(value, (date, datetime)):
                value = value.isoformat()
            row_dict[column] = value
        json_answer.append(row_dict)
    return json_answer


def trim_content(content, max_tokens=110 * 1000):
    # Token size is 3 characters per token
    token_size = 3
    trimmed_content = ""

    # Iterate through the content in chunks of 3 characters (each chunk is 1 token)
    for i in range(0, len(content), token_size):
        if len(trimmed_content) // token_size < max_tokens:
            trimmed_content += content[i : i + token_size]
        else:
            break

    return trimmed_content


def summary_answer(user_question, json_result, history, summary_prompt):
    """
    Uses Claude API to generate a summary response based on input data.
    """
    client = anthropic.Anthropic(api_key=settings.ANTHROPIC_API_KEY)

    # Construct the messages in the Anthropic format
    messages = []

    for message in history:
        # Ensure message[0] and message[1] are strings
        user_content = str(message[0]) if message[0] is not None else ""
        assistant_content = str(message[1]) if message[1] is not None else ""

        messages.append(
            {"role": "user", "content": [{"type": "text", "text": user_content}]}
        )
        messages.append(
            {
                "role": "assistant",
                "content": [{"type": "text", "text": assistant_content}],
            }
        )

    messages.append(
        {
            "role": "user",
            "content": [
                {"type": "text", "text": user_question},
                {"type": "text", "text": json.dumps(json_result)},
            ],
        }
    )

    try:
        # Send the request to Claude's API
        response = client.messages.create(
            model=settings.CLAUDE_MODEL_NAME,
            max_tokens=4096,
            temperature=0.2,
            system=summary_prompt,
            messages=messages,
        )

        # Ensure response is formatted correctly
        return (
            response.content[0].text.strip()
            if response.content
            else "No valid response received."
        )

    except Exception as e:
        if "context_length_exceeded" in str(e):
            return "The model can't handle this much data. Please specify a time period for a better response."
        else:
            return e  # Re-raise for debugging


def stream_summary_answer(user_question, json_result, history, summary_prompt):
    """
    Uses Claude API to generate a summary response based on input data.
    """
    import time
    start_time = time.time()

    client = anthropic.Anthropic(api_key=settings.ANTHROPIC_API_KEY)

    # Construct the messages in the Anthropic format
    messages = []

    for message in history:
        # Ensure message[0] and message[1] are strings
        user_content = str(message[0]) if message[0] is not None else ""
        assistant_content = str(message[1]) if message[1] is not None else ""

        messages.append(
            {"role": "user", "content": [{"type": "text", "text": user_content}]}
        )
        messages.append(
            {
                "role": "assistant",
                "content": [{"type": "text", "text": assistant_content}],
            }
        )

    messages.append(
        {
            "role": "user",
            "content": [
                {"type": "text", "text": user_question},
                {"type": "text", "text": json.dumps(json_result)},
            ],
        }
    )

    try:
        resp = ""
        chunks = []
        # Send the request to Claude's API
        with client.messages.stream(
            model=settings.CLAUDE_MODEL_NAME,
            max_tokens=4096,
            temperature=0.2,
            system=summary_prompt,
            messages=messages,
        ) as response:
            # Ensure response is formatted correctly
            for chunk in response.text_stream:
                if chunk:
                    resp += chunk
                    chunks.append(chunk)
                    yield chunk
        print("response=======================>", resp)
        print("chunks=======================>", chunks)

        # Track cost with comprehensive tracker
        duration_ms = (time.time() - start_time) * 1000
        from app.utils.comprehensive_cost_tracker import cost_tracker
        input_text = f"{summary_prompt}\n{user_question}\n{json.dumps(json_result)}"
        cost_tracker.track_model_usage(
            model_name=settings.CLAUDE_MODEL_NAME,
            function_name="stream_summary_answer",
            input_text=input_text,
            output_text=resp,
            duration_ms=duration_ms
        )

    except Exception as e:
        if "context_length_exceeded" in str(e):
            yield "The model can't handle this much data. Please specify a time period for a better response."
        else:
            yield e  # Re-raise for debugging


def stream_summary_answer_v3(user_question, json_result, history, summary_prompt):
    """
    Uses Claude API to generate a summary response based on input data.
    """

    llm = ChatAnthropic(
        model=settings.CLAUDE_MODEL_NAME,
        max_tokens=4096,
        temperature=0.2,
        streaming=True,
        anthropic_api_key=settings.ANTHROPIC_API_KEY,
    )

    # Construct message history
    messages = []

    for message in history:
        user_content = str(message[0]) if message[0] is not None else ""
        assistant_content = str(message[1]) if message[1] is not None else ""

        messages.append(HumanMessage(content=user_content))
        messages.append(AIMessage(content=assistant_content))

    # Add current question and JSON data
    current_content = f"{user_question}\n{json.dumps(json_result)}"
    messages.append(HumanMessage(content=current_content))

    # Add system message at the beginning
    messages.insert(0, SystemMessage(content=summary_prompt))

    try:
        resp = ""
        chunks = []

        # Stream the response
        for chunk in llm.stream(messages):
            if chunk.content:
                resp += chunk.content
                chunks.append(chunk.content)
                yield chunk.content

        print("response=======================>", resp)
        print("chunks=======================>", chunks)

    except Exception as e:
        if "context_length_exceeded" in str(e):
            yield "The model can't handle this much data. Please specify a time period for a better response."
        else:
            yield str(e)


def extract_json_content(response):
    matches = re.findall(r"\{[^}]*\}", response, re.DOTALL)
    if matches:
        try:
            # Attempt to parse the first match as JSON to ensure it's valid
            json_content = json.loads(matches[0])
            return json_content
        except json.JSONDecodeError:
            return None
    else:
        return None


async def save_generic_image(file_name: str, file: UploadFile):
    path = await upload_to_s3_as_public(path=file_name, file=file)
    return path


async def delete_generic_image(s3_key: str):
    await delete_public_file_from_s3(s3_key)
    return True


# Settings for temporary directory
TMP_DIR = "temp_files"
os.makedirs(TMP_DIR, exist_ok=True)


def save_image_to_temp(encoded_image: str, extension: str) -> str:
    """Save Base64-encoded image to a temporary file."""
    file_name = f"{uuid.uuid4()}.{extension}"
    file_path = Path(TMP_DIR) / file_name
    with open(file_path, "wb") as image_file:
        image_file.write(base64.b64decode(encoded_image))

    logger.info(f"Image saved to {file_path}")
    return str(file_path)

    # Data conversion function


def safe_convert_int(value, default=0):
    try:
        # Handle both int strings and float strings
        return int(float(value))
    except (ValueError, TypeError):
        return default


def safe_convert_float(value, default=0.0):
    try:
        return float(value)
    except (ValueError, TypeError):
        return default


def convert_response(response_data: dict) -> dict:
    bullish = safe_convert_int(response_data.get("bullish_strength", 0))
    bearish = safe_convert_int(response_data.get("bearish_strength", 0))
    stock_price_float = safe_convert_float(response_data.get("stock_price", 0))
    # Convert to cents as integer
    stock_price_cents = int(stock_price_float * 100)

    return {
        "bullish_strength": bullish,
        "bearish_strength": bearish,
        "stock_price": stock_price_cents,
        "currency": (response_data.get("currency", "")[:3].upper()),
        "date_range": response_data.get("date_range", ""),
        "timeframe": response_data.get("timeframe", ""),
        "symbol": response_data.get("symbol", ""),
        "title": response_data.get("title", ""),
    }


def prepare_dataset(question: str, answer: dict):
    date_str = datetime.now().strftime("%Y-%m-%d")
    if not os.path.exists("datasets"):
        os.makedirs("datasets")
    filename = f"datasets/dataset_{date_str}.json"

    # Check if the file exists and load existing data
    if os.path.exists(filename):
        with open(filename, "r", encoding="utf-8") as f:
            try:
                data = json.load(f)
                if not isinstance(data, list):
                    data = []
            except json.JSONDecodeError:
                data = []
    else:
        data = []

    # Append new QA pair
    data.append({"question": question, "answer": answer})

    # Write back to file
    with open(filename, "w", encoding="utf-8") as f:
        json.dump(data, f, indent=4, ensure_ascii=False)

    print(f"Saved QA pair to {filename}")


async def summary_stream_handler(user_question, json_result, history, summary_prompt):
    inside_json = False
    json_content = ""
    answer_content = ""

    for chunk in stream_summary_answer(
        user_question=user_question,
        json_result=json_result,
        history=history,
        summary_prompt=summary_prompt,
    ):
        if inside_json:
            json_content += chunk
        else:
            if "`" in chunk:
                inside_json = True
                idx = chunk.find("`")
                before_backtick = chunk[:idx]
                after_backtick = chunk[idx:]
                if before_backtick:
                    yield {
                        "type": "answer",
                        "data": before_backtick,
                    }
                json_content += after_backtick
            else:
                yield {
                    "type": "answer",
                    "data": chunk,
                }
    yield {
        "type": "final",
        "answer": answer_content,
        "data": json.loads(json_content.replace("```json", "").replace("`", ""))
        if json_content
        else None,
    }


async def summary_stream_handler_v3(
    user_question, json_result, history, summary_prompt
):
    inside_json = False
    json_content = ""
    answer_content = ""

    for chunk in stream_summary_answer(
        user_question=user_question,
        json_result=json_result,
        history=history,
        summary_prompt=summary_prompt,
    ):
        if inside_json:
            json_content += chunk
        else:
            if "`" in chunk:
                inside_json = True
                idx = chunk.find("`")
                before_backtick = chunk[:idx]
                after_backtick = chunk[idx:]
                if before_backtick:
                    yield {
                        "type": "answer",
                        "data": before_backtick,
                    }
                json_content += after_backtick
            else:
                yield {
                    "type": "answer",
                    "data": chunk,
                }
    yield {
        "type": "final",
        "answer": answer_content,
        "data": json.loads(json_content.replace("```json", "").replace("`", ""))
        if json_content
        else None,
    }


async def websearch_stream_handler(user_question):
    inside_json = False
    json_content = ""
    answer_content = ""

    try:
        async for chunk in web_scrape_search_stream(search_query=user_question):
            if inside_json:
                json_content += chunk
            else:
                if "`" in chunk:
                    inside_json = True
                    idx = chunk.find("`")
                    before_backtick = chunk[:idx]
                    after_backtick = chunk[idx:]
                    if before_backtick:
                        yield {
                            "type": "answer",
                            "data": before_backtick,
                        }
                    json_content += after_backtick
                else:
                    yield {
                        "type": "answer",
                        "data": chunk,
                    }
    except StopIteration:
        pass
    yield {
        "type": "final",
        "answer": answer_content,
        "data": json.loads(json_content.replace("```json", "").replace("`", ""))
        if json_content
        else None,
    }


async def websearch_stream_handler_v3(user_question):
    inside_json = False
    json_content = ""
    answer_content = ""

    try:
        async for chunk in web_scrape_search_stream(search_query=user_question):
            if inside_json:
                json_content += chunk
            else:
                if "`" in chunk:
                    inside_json = True
                    idx = chunk.find("`")
                    before_backtick = chunk[:idx]
                    after_backtick = chunk[idx:]
                    if before_backtick:
                        yield {
                            "type": "answer",
                            "data": before_backtick,
                        }
                    json_content += after_backtick
                else:
                    yield {
                        "type": "answer",
                        "data": chunk,
                    }
    except StopIteration:
        pass
    yield {
        "type": "final",
        "answer": answer_content,
        "data": json.loads(json_content.replace("```json", "").replace("`", ""))
        if json_content
        else None,
    }
