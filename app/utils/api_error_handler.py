"""
API Error Handling Utilities

This module provides robust error handling for external API calls with retry mechanisms,
circuit breaker patterns, and standardized error responses.
"""

import asyncio
import functools
import logging
import time
from datetime import datetime
from enum import Enum
from typing import Any, Callable, Dict, List, Optional, Type, TypeVar, Union, cast

import aiohttp
import requests
from botocore.exceptions import ClientError
from fastapi import HTTPException
from openai import APIError, APITimeoutError, RateLimitError
from tenacity import (
    retry,
    retry_if_exception_type,
    stop_after_attempt,
    wait_exponential,
)

from app.utils.error_handler import (
    ErrorCategory,
    ErrorCode,
    ErrorSeverity,
    ExternalAPIError,
    StandardizedError,
)
from app.utils.logger import chatbot_logger as logger

# Type variable for generic function return type
T = TypeVar("T")


class CircuitBreakerState(Enum):
    """Circuit breaker states"""
    CLOSED = "closed"  # Normal operation, requests flow through
    OPEN = "open"      # Failing state, requests are blocked
    HALF_OPEN = "half_open"  # Testing state, limited requests allowed


class CircuitBreaker:
    """
    Circuit breaker implementation to prevent cascading failures
    when external services are unavailable.
    """
    
    _instances: Dict[str, "CircuitBreaker"] = {}
    
    @classmethod
    def get_instance(cls, service_name: str) -> "CircuitBreaker":
        """Get or create a circuit breaker instance for a service"""
        if service_name not in cls._instances:
            cls._instances[service_name] = CircuitBreaker(service_name)
        return cls._instances[service_name]
    
    def __init__(
        self,
        service_name: str,
        failure_threshold: int = 5,
        recovery_timeout: int = 30,
        half_open_max_calls: int = 3
    ):
        self.service_name = service_name
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.half_open_max_calls = half_open_max_calls
        
        self.state = CircuitBreakerState.CLOSED
        self.failure_count = 0
        self.last_failure_time = None
        self.half_open_calls = 0
    
    def record_success(self):
        """Record a successful call"""
        if self.state == CircuitBreakerState.HALF_OPEN:
            self.half_open_calls += 1
            if self.half_open_calls >= self.half_open_max_calls:
                logger.info(f"Circuit breaker for {self.service_name} closing after successful test calls")
                self.state = CircuitBreakerState.CLOSED
                self.failure_count = 0
                self.half_open_calls = 0
        elif self.state == CircuitBreakerState.CLOSED:
            self.failure_count = 0
    
    def record_failure(self):
        """Record a failed call"""
        self.last_failure_time = time.time()
        
        if self.state == CircuitBreakerState.HALF_OPEN:
            logger.warning(f"Circuit breaker for {self.service_name} reopening after failed test call")
            self.state = CircuitBreakerState.OPEN
            self.half_open_calls = 0
        elif self.state == CircuitBreakerState.CLOSED:
            self.failure_count += 1
            if self.failure_count >= self.failure_threshold:
                logger.warning(f"Circuit breaker for {self.service_name} opening after {self.failure_count} failures")
                self.state = CircuitBreakerState.OPEN
    
    def allow_request(self) -> bool:
        """Check if a request should be allowed"""
        if self.state == CircuitBreakerState.CLOSED:
            return True
        
        if self.state == CircuitBreakerState.OPEN:
            # Check if recovery timeout has elapsed
            if self.last_failure_time and time.time() - self.last_failure_time > self.recovery_timeout:
                logger.info(f"Circuit breaker for {self.service_name} entering half-open state")
                self.state = CircuitBreakerState.HALF_OPEN
                self.half_open_calls = 0
                return True
            return False
        
        # HALF_OPEN: Allow limited test requests
        return self.half_open_calls < self.half_open_max_calls


def with_circuit_breaker(service_name: str):
    """
    Decorator to apply circuit breaker pattern to a function
    
    Args:
        service_name: Name of the service being called
    """
    def decorator(func):
        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs):
            circuit_breaker = CircuitBreaker.get_instance(service_name)
            
            if not circuit_breaker.allow_request():
                raise ExternalAPIError(
                    service=service_name,
                    message=f"Service {service_name} is currently unavailable (circuit open)",
                    severity=ErrorSeverity.HIGH,
                    additional_info={"circuit_state": circuit_breaker.state.value}
                )
            
            try:
                result = await func(*args, **kwargs)
                circuit_breaker.record_success()
                return result
            except Exception as e:
                circuit_breaker.record_failure()
                raise e
        
        @functools.wraps(func)
        def sync_wrapper(*args, **kwargs):
            circuit_breaker = CircuitBreaker.get_instance(service_name)
            
            if not circuit_breaker.allow_request():
                raise ExternalAPIError(
                    service=service_name,
                    message=f"Service {service_name} is currently unavailable (circuit open)",
                    severity=ErrorSeverity.HIGH,
                    additional_info={"circuit_state": circuit_breaker.state.value}
                )
            
            try:
                result = func(*args, **kwargs)
                circuit_breaker.record_success()
                return result
            except Exception as e:
                circuit_breaker.record_failure()
                raise e
        
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        return sync_wrapper
    
    return decorator


def handle_openai_error(func):
    """
    Decorator to handle OpenAI API errors
    
    Args:
        func: Function to decorate
    """
    @functools.wraps(func)
    async def async_wrapper(*args, **kwargs):
        try:
            return await func(*args, **kwargs)
        except RateLimitError as e:
            raise ExternalAPIError(
                service="openai",
                message="OpenAI API rate limit exceeded",
                status_code=429,
                original_exception=e,
                additional_info={"retry_after": e.headers.get("retry-after") if hasattr(e, "headers") else None}
            )
        except APITimeoutError as e:
            raise ExternalAPIError(
                service="openai",
                message="OpenAI API request timed out",
                status_code=408,
                original_exception=e
            )
        except APIError as e:
            raise ExternalAPIError(
                service="openai",
                message=f"OpenAI API error: {str(e)}",
                status_code=getattr(e, "status_code", 500),
                original_exception=e
            )
        except Exception as e:
            raise ExternalAPIError(
                service="openai",
                message=f"Unexpected error calling OpenAI API: {str(e)}",
                original_exception=e
            )
    
    @functools.wraps(func)
    def sync_wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except RateLimitError as e:
            raise ExternalAPIError(
                service="openai",
                message="OpenAI API rate limit exceeded",
                status_code=429,
                original_exception=e,
                additional_info={"retry_after": e.headers.get("retry-after") if hasattr(e, "headers") else None}
            )
        except APITimeoutError as e:
            raise ExternalAPIError(
                service="openai",
                message="OpenAI API request timed out",
                status_code=408,
                original_exception=e
            )
        except APIError as e:
            raise ExternalAPIError(
                service="openai",
                message=f"OpenAI API error: {str(e)}",
                status_code=getattr(e, "status_code", 500),
                original_exception=e
            )
        except Exception as e:
            raise ExternalAPIError(
                service="openai",
                message=f"Unexpected error calling OpenAI API: {str(e)}",
                original_exception=e
            )
    
    if asyncio.iscoroutinefunction(func):
        return async_wrapper
    return sync_wrapper


def handle_s3_error(func):
    """
    Decorator to handle AWS S3 errors
    
    Args:
        func: Function to decorate
    """
    @functools.wraps(func)
    async def async_wrapper(*args, **kwargs):
        try:
            return await func(*args, **kwargs)
        except ClientError as e:
            error_code = e.response.get("Error", {}).get("Code", "Unknown")
            error_message = e.response.get("Error", {}).get("Message", str(e))
            
            raise ExternalAPIError(
                service="s3",
                message=f"S3 API error: {error_message}",
                status_code=e.response.get("ResponseMetadata", {}).get("HTTPStatusCode", 500),
                original_exception=e,
                additional_info={"error_code": error_code}
            )
        except Exception as e:
            raise ExternalAPIError(
                service="s3",
                message=f"Unexpected error calling S3 API: {str(e)}",
                original_exception=e
            )
    
    return async_wrapper
