import io
import uuid

import aioboto3
from botocore.exceptions import ClientError
from fastapi import UploadFile

from app.core.config import settings
from app.utils.api_error_handler import handle_s3_error, with_circuit_breaker
from app.utils.error_handler import FileOperationError


# Asynchronous S3 client to handle development and production
async def get_s3_client():
    session = aioboto3.Session()

    if settings.ENVIRONMENT == "development":
        return session.client(
            "s3",
            aws_access_key_id=settings.AWS_ACCESS_KEY,
            aws_secret_access_key=settings.AWS_SECRET_KEY,
            region_name=settings.AWS_REGION,
        )
    else:
        # Explicitly use the EC2 instance's IAM role
        return session.client(
            "s3", region_name=settings.AWS_REGION  # Ensure the region is set!
        )


# Asynchronously upload a file to S3 (non-avatar, using original filename)
@handle_s3_error
@with_circuit_breaker("s3_upload")
async def upload_file_to_s3(account_id: str, file: UploadFile):
    s3_key = f"private/ai/agents/users/{account_id}/{file.filename}"

    async with await get_s3_client() as s3:
        try:
            await s3.upload_fileobj(file.file, settings.S3_BUCKET_NAME, s3_key)
            print(
                f"File '{file.filename}' uploaded to folder '{account_id}' in bucket '{settings.S3_BUCKET_NAME}'"
            )
            return s3_key
        except ClientError as e:
            # This will be caught by the handle_s3_error decorator
            raise FileOperationError(
                message=f"Failed to upload file to S3: {str(e)}",
                file_path=s3_key,
                operation="upload",
                original_exception=e,
                additional_info={
                    "account_id": account_id,
                    "filename": file.filename,
                    "bucket": settings.S3_BUCKET_NAME
                }
            )


# Asynchronously upload an avatar with a unique S3 key using UUID
async def upload_to_s3_as_public(path: str, file: UploadFile) -> str:
    file_extension = file.filename.split(".")[-1]
    s3_key = f"public/ai/agents/{path}/{uuid.uuid4()}.{file_extension}"

    async with await get_s3_client() as s3:
        try:
            extra_args = {"ContentType": file.content_type}
            # if settings.ENVIRONMENT == "development":
            #     extra_args["ACL"] = "public-read"
            await s3.upload_fileobj(
                file.file,
                settings.S3_BUCKET_NAME,
                s3_key,
                ExtraArgs=extra_args,
            )
            print(
                f"Avatar '{file.filename}' uploaded to folder '{path}' in bucket '{settings.S3_BUCKET_NAME}'"
            )
            # s3_host = (
            #     settings.S3_HOSTNAME
            #     if settings.ENVIRONMENT != "development"
            #     else settings.S3_BUCKET_NAME
            # )
            url = ""
            # if settings.ENVIRONMENT == "development":
            #     return f"https://{settings.S3_BUCKET_NAME}.s3.amazonaws.com/{s3_key}"

            # else:
            return f"https://{settings.S3_HOSTNAME}/{s3_key}"

        except ClientError as e:
            raise Exception(f"Failed to upload avatar to S3: {e}")


async def delete_public_file_from_s3(file_path: str):
    if not file_path:
        return None
    s3_key = file_path.split("public/")[1]
    s3_key = "public/" + s3_key
    async with await get_s3_client() as s3:
        try:
            if await check_public_file_exists_in_s3(s3_key):
                print(f"Deleting file {s3_key} from bucket {settings.S3_BUCKET_NAME}.")
                await s3.delete_object(Bucket=settings.S3_BUCKET_NAME, Key=s3_key)
        except ClientError as e:
            raise Exception(f"Error deleting file from S3: {e}")


async def check_public_file_exists_in_s3(file_path: str) -> bool:
    s3_key = file_path.split("public/")[1]
    s3_key = "public/" + s3_key
    async with await get_s3_client() as s3:
        try:
            await s3.head_object(Bucket=settings.S3_BUCKET_NAME, Key=s3_key)
            return True
        except ClientError as e:
            if e.response["Error"]["Code"] == "404":
                return False
            else:
                raise


# create these 3 methods for generic public file upload
async def check_public_file_exists(path: str) -> bool:
    async with await get_s3_client() as s3:
        try:
            await s3.head_object(Bucket=settings.S3_BUCKET_NAME, Key=path)
            return True
        except ClientError as e:
            if e.response["Error"]["Code"] == "404":
                return False
            else:
                raise


# async def upload_as_public(path: str, file: UploadFile) -> str:
#     if await check_public_file_exists(path):
#         await delete_public_file(path)
#     file_extension = file.filename.split(".")[-1]
#     s3_key = f"public/ai/{path}"

#     async with await get_s3_client() as s3:
#         try:
#             extra_args = {"ContentType": file.content_type}
#             await s3.upload_fileobj(
#                 file.file,
#                 settings.S3_BUCKET_NAME,
#                 s3_key,
#                 ExtraArgs=extra_args,
#             )
#             return f"https://{settings.S3_HOSTNAME}/{s3_key}"
#         except ClientError as e:
#             raise Exception(f"Failed to upload file to S3: {e}")


async def upload_as_public(path: str, file: UploadFile) -> str:
    if await check_public_file_exists(path):
        await delete_public_file(path)

    file_extension = file.filename.split(".")[-1]
    s3_key = f"public/ai/{path}"

    async with await get_s3_client() as s3:
        try:
            file.file.seek(0)  # 🔥 Important!
            extra_args = {"ContentType": file.content_type}
            await s3.upload_fileobj(
                file.file,
                settings.S3_BUCKET_NAME,
                s3_key,
                ExtraArgs=extra_args,
            )
            return f"https://{settings.S3_HOSTNAME}/{s3_key}"
        except ClientError as e:
            raise Exception(f"Failed to upload file to S3: {e}")


async def delete_public_file(path: str):
    path = "public/ai/" + path.split("public/ai/")[1]
    if await check_public_file_exists(path):
        async with await get_s3_client() as s3:
            try:
                await s3.delete_object(Bucket=settings.S3_BUCKET_NAME, Key=path)
            except ClientError as e:
                raise Exception(f"Error deleting file from S3: {e}")


# Asynchronously check if a file exists in S3
async def check_file_exists_in_s3(account_id: str, file_name: str) -> bool:
    s3_key = f"private/ai/agents/users/{account_id}/{file_name}"

    async with await get_s3_client() as s3:
        try:
            await s3.head_object(Bucket=settings.S3_BUCKET_NAME, Key=s3_key)
            return True
        except ClientError as e:
            if e.response["Error"]["Code"] == "404":
                return False
            else:
                raise


# Asynchronously download a file from S3
async def download_file_from_s3(account_id: str, file_name: str):
    s3_key = f"private/ai/agents/users/{account_id}/{file_name}"

    async with await get_s3_client() as s3:
        try:
            file_obj = await s3.get_object(Bucket=settings.S3_BUCKET_NAME, Key=s3_key)
            return await file_obj["Body"].read()
        except ClientError as e:
            raise Exception(f"Error downloading file from S3: {e}")


# Asynchronously delete a file from S3
async def delete_file_from_s3(path: str, file_name: str):
    s3_key = f"private/ai/agents/users/{path}/{file_name}"

    async with await get_s3_client() as s3:
        try:
            if await check_file_exists_in_s3(path, file_name):
                print(f"Deleting file {s3_key} from bucket {settings.S3_BUCKET_NAME}.")
                await s3.delete_object(Bucket=settings.S3_BUCKET_NAME, Key=s3_key)
        except ClientError as e:
            raise Exception(f"Error deleting file from S3: {e}")


async def upload_file_image_to_s3(task_id: int, file: UploadFile):
    filename = file.filename
    # if folder_structure else unique_file_name
    s3_key = f"public/ai/vuevat/projects/{task_id}/{filename}"

    async with await get_s3_client() as s3:
        try:
            if settings.ENVIRONMENT == "development":
                # await s3.upload_fileobj(file.file, settings.S3_BUCKET_NAME, s3_key, ExtraArgs={'ACL': 'public-read', "ContentType": file.content_type})
                await s3.upload_fileobj(
                    file.file,
                    settings.S3_BUCKET_NAME,
                    s3_key,
                    ExtraArgs={"ContentType": file.content_type},
                )
                # return f"https://{settings.S3_BUCKET_NAME}.s3.amazonaws.com/{s3_key}"

            else:
                await s3.upload_fileobj(
                    file.file,
                    settings.S3_BUCKET_NAME,
                    s3_key,
                    ExtraArgs={"ContentType": file.content_type},
                )
            return f"https://{settings.S3_HOSTNAME}/{s3_key}"
        except ClientError as e:
            raise Exception(f"Failed to upload file to S3: {e}")


async def upload_jsonl_image_to_s3(task_id: int, annotation_data: str):
    s3_key = f"public/ai/vuevat/annotations/{task_id}/" + f"{uuid.uuid4().hex}.jsonl"
    async with await get_s3_client() as s3:
        try:
            file = io.BytesIO(annotation_data.encode("utf-8"))
            if settings.ENVIRONMENT == "development":
                # await s3.upload_fileobj(file, settings.S3_BUCKET_NAME, s3_key, ExtraArgs={'ACL': 'public-read'})
                await s3.upload_fileobj(file, settings.S3_BUCKET_NAME, s3_key)
                # return f"https://{settings.S3_BUCKET_NAME}.s3.amazonaws.com/{s3_key}"

            else:
                await s3.upload_fileobj(file, settings.S3_BUCKET_NAME, s3_key)
            return f"https://{settings.S3_HOSTNAME}/{s3_key}"
            # return s3_key
        except ClientError as e:
            raise Exception(f"Failed to upload file to S3: {e}")
