"""
Comprehensive Error Handling Framework for Microservice Chat Backend

This module provides standardized error handling patterns, custom exceptions,
and error response formatting for consistent error management across the application.
"""

import json
import logging
import traceback
from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional, Union

from fastapi import HTTPException, Request
from fastapi.responses import JSONResponse
from pydantic import BaseModel

from app.services.mail_service import send_alert_mail
from app.utils.logger import chatbot_logger as logger


class ErrorCategory(str, Enum):
    """Error categories for classification and handling"""
    VALIDATION = "validation"
    AUTHENTICATION = "authentication"
    AUTHORIZATION = "authorization"
    EXTERNAL_API = "external_api"
    DATABASE = "database"
    FILE_OPERATION = "file_operation"
    BUSINESS_LOGIC = "business_logic"
    SYSTEM = "system"
    NETWORK = "network"
    TIMEOUT = "timeout"


class ErrorSeverity(str, Enum):
    """Error severity levels for monitoring and alerting"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class ErrorCode(str, Enum):
    """Standardized error codes"""
    # Validation Errors (1000-1999)
    INVALID_INPUT = "ERR_1001"
    MISSING_REQUIRED_FIELD = "ERR_1002"
    INVALID_FORMAT = "ERR_1003"
    INVALID_FILE_TYPE = "ERR_1004"
    FILE_TOO_LARGE = "ERR_1005"
    
    # Authentication/Authorization Errors (2000-2999)
    INVALID_TOKEN = "ERR_2001"
    TOKEN_EXPIRED = "ERR_2002"
    INSUFFICIENT_PERMISSIONS = "ERR_2003"
    ACCOUNT_LOCKED = "ERR_2004"
    
    # External API Errors (3000-3999)
    OPENAI_API_ERROR = "ERR_3001"
    PERPLEXITY_API_ERROR = "ERR_3002"
    S3_API_ERROR = "ERR_3003"
    FMP_API_ERROR = "ERR_3004"
    EXTERNAL_API_TIMEOUT = "ERR_3005"
    EXTERNAL_API_RATE_LIMIT = "ERR_3006"
    
    # Database Errors (4000-4999)
    DATABASE_CONNECTION_ERROR = "ERR_4001"
    DATABASE_QUERY_ERROR = "ERR_4002"
    DATABASE_TIMEOUT = "ERR_4003"
    DATABASE_CONSTRAINT_VIOLATION = "ERR_4004"
    
    # File Operation Errors (5000-5999)
    FILE_NOT_FOUND = "ERR_5001"
    FILE_UPLOAD_FAILED = "ERR_5002"
    FILE_PROCESSING_ERROR = "ERR_5003"
    S3_UPLOAD_FAILED = "ERR_5004"
    
    # Business Logic Errors (6000-6999)
    RESOURCE_NOT_FOUND = "ERR_6001"
    DUPLICATE_RESOURCE = "ERR_6002"
    OPERATION_NOT_ALLOWED = "ERR_6003"
    QUOTA_EXCEEDED = "ERR_6004"
    
    # System Errors (7000-7999)
    INTERNAL_SERVER_ERROR = "ERR_7001"
    SERVICE_UNAVAILABLE = "ERR_7002"
    CONFIGURATION_ERROR = "ERR_7003"
    
    # Network Errors (8000-8999)
    CONNECTION_TIMEOUT = "ERR_8001"
    NETWORK_UNREACHABLE = "ERR_8002"
    DNS_RESOLUTION_ERROR = "ERR_8003"


class ErrorDetail(BaseModel):
    """Detailed error information"""
    code: ErrorCode
    message: str
    category: ErrorCategory
    severity: ErrorSeverity
    timestamp: datetime
    request_id: Optional[str] = None
    user_id: Optional[str] = None
    additional_info: Optional[Dict[str, Any]] = None


class StandardizedError(Exception):
    """Base class for all standardized errors"""
    
    def __init__(
        self,
        code: ErrorCode,
        message: str,
        category: ErrorCategory,
        severity: ErrorSeverity = ErrorSeverity.MEDIUM,
        additional_info: Optional[Dict[str, Any]] = None,
        original_exception: Optional[Exception] = None
    ):
        self.code = code
        self.message = message
        self.category = category
        self.severity = severity
        self.additional_info = additional_info or {}
        self.original_exception = original_exception
        self.timestamp = datetime.utcnow()
        super().__init__(self.message)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert error to dictionary format"""
        return {
            "code": self.code.value,
            "message": self.message,
            "category": self.category.value,
            "severity": self.severity.value,
            "timestamp": self.timestamp.isoformat(),
            "additional_info": self.additional_info
        }


class ValidationError(StandardizedError):
    """Validation-related errors"""

    def __init__(self, message: str, field: Optional[str] = None, **kwargs):
        additional_info = kwargs.get("additional_info", {})
        if field:
            additional_info["field"] = field

        # Filter out parameters that are explicitly set to avoid conflicts
        filtered_kwargs = {k: v for k, v in kwargs.items()
                          if k not in ['code', 'message', 'category', 'severity', 'additional_info']}

        super().__init__(
            code=ErrorCode.INVALID_INPUT,
            message=message,
            category=ErrorCategory.VALIDATION,
            severity=ErrorSeverity.LOW,
            additional_info=additional_info,
            **filtered_kwargs
        )


class ExternalAPIError(StandardizedError):
    """External API-related errors"""

    def __init__(
        self,
        service: str,
        message: str,
        status_code: Optional[int] = None,
        **kwargs
    ):
        additional_info = kwargs.get("additional_info", {})
        additional_info.update({
            "service": service,
            "status_code": status_code
        })

        # Map service to specific error code
        code_mapping = {
            "openai": ErrorCode.OPENAI_API_ERROR,
            "perplexity": ErrorCode.PERPLEXITY_API_ERROR,
            "s3": ErrorCode.S3_API_ERROR,
            "fmp": ErrorCode.FMP_API_ERROR,
        }

        # Filter out parameters that are explicitly set to avoid conflicts
        filtered_kwargs = {k: v for k, v in kwargs.items()
                          if k not in ['code', 'message', 'category', 'severity', 'additional_info']}

        super().__init__(
            code=code_mapping.get(service.lower(), ErrorCode.EXTERNAL_API_TIMEOUT),
            message=message,
            category=ErrorCategory.EXTERNAL_API,
            severity=ErrorSeverity.HIGH,
            additional_info=additional_info,
            **filtered_kwargs
        )


class DatabaseError(StandardizedError):
    """Database-related errors"""

    def __init__(self, message: str, operation: Optional[str] = None, **kwargs):
        additional_info = kwargs.get("additional_info", {})
        if operation:
            additional_info["operation"] = operation

        # Filter out parameters that are explicitly set to avoid conflicts
        filtered_kwargs = {k: v for k, v in kwargs.items()
                          if k not in ['code', 'message', 'category', 'severity', 'additional_info']}

        super().__init__(
            code=ErrorCode.DATABASE_QUERY_ERROR,
            message=message,
            category=ErrorCategory.DATABASE,
            severity=ErrorSeverity.HIGH,
            additional_info=additional_info,
            **filtered_kwargs
        )


class FileOperationError(StandardizedError):
    """File operation-related errors"""

    def __init__(
        self,
        message: str,
        file_path: Optional[str] = None,
        operation: Optional[str] = None,
        **kwargs
    ):
        additional_info = kwargs.get("additional_info", {})
        additional_info.update({
            "file_path": file_path,
            "operation": operation
        })

        # Filter out parameters that are explicitly set to avoid conflicts
        filtered_kwargs = {k: v for k, v in kwargs.items()
                          if k not in ['code', 'message', 'category', 'severity', 'additional_info']}

        super().__init__(
            code=ErrorCode.FILE_PROCESSING_ERROR,
            message=message,
            category=ErrorCategory.FILE_OPERATION,
            severity=ErrorSeverity.MEDIUM,
            additional_info=additional_info,
            **filtered_kwargs
        )


class BusinessLogicError(StandardizedError):
    """Business logic-related errors"""

    def __init__(self, message: str, resource_type: Optional[str] = None, **kwargs):
        additional_info = kwargs.get("additional_info", {})
        if resource_type:
            additional_info["resource_type"] = resource_type

        # Filter out parameters that are explicitly set to avoid conflicts
        filtered_kwargs = {k: v for k, v in kwargs.items()
                          if k not in ['code', 'message', 'category', 'severity', 'additional_info']}

        super().__init__(
            code=ErrorCode.OPERATION_NOT_ALLOWED,
            message=message,
            category=ErrorCategory.BUSINESS_LOGIC,
            severity=ErrorSeverity.MEDIUM,
            additional_info=additional_info,
            **filtered_kwargs
        )


class ErrorHandler:
    """Centralized error handling and response formatting"""
    
    @staticmethod
    def log_error(error: StandardizedError, request: Optional[Request] = None):
        """Log error with appropriate level based on severity"""
        log_data = {
            "error_code": error.code.value,
            "error_message": error.message,
            "category": error.category.value,
            "severity": error.severity.value,
            "timestamp": error.timestamp.isoformat(),
            "additional_info": error.additional_info
        }

        if request:
            log_data.update({
                "method": request.method,
                "url": str(request.url),
                "user_agent": request.headers.get("user-agent"),
                "client_ip": request.client.host if request.client else None
            })

        if error.original_exception:
            log_data["original_exception"] = str(error.original_exception)
            log_data["traceback"] = traceback.format_exception(
                type(error.original_exception),
                error.original_exception,
                error.original_exception.__traceback__
            )

        # Record error in monitoring system
        try:
            from app.utils.error_monitoring import monitor_error
            monitor_error(error, log_data)
        except ImportError:
            # Monitoring system not available
            pass

        # Log based on severity
        if error.severity in [ErrorSeverity.CRITICAL, ErrorSeverity.HIGH]:
            logger.error(f"Error: {json.dumps(log_data, indent=2)}")
            # Send alert for critical/high severity errors
            if error.severity == ErrorSeverity.CRITICAL:
                send_alert_mail(f"Critical Error: {error.message}\n\n{json.dumps(log_data, indent=2)}")
        elif error.severity == ErrorSeverity.MEDIUM:
            logger.warning(f"Warning: {json.dumps(log_data, indent=2)}")
        else:
            logger.info(f"Info: {json.dumps(log_data, indent=2)}")
    
    @staticmethod
    def create_error_response(
        error: StandardizedError,
        request: Optional[Request] = None
    ) -> JSONResponse:
        """Create standardized error response"""
        ErrorHandler.log_error(error, request)
        
        # Map error categories to HTTP status codes
        status_code_mapping = {
            ErrorCategory.VALIDATION: 400,
            ErrorCategory.AUTHENTICATION: 401,
            ErrorCategory.AUTHORIZATION: 403,
            ErrorCategory.BUSINESS_LOGIC: 404,
            ErrorCategory.EXTERNAL_API: 502,
            ErrorCategory.DATABASE: 503,
            ErrorCategory.FILE_OPERATION: 500,
            ErrorCategory.SYSTEM: 500,
            ErrorCategory.NETWORK: 502,
            ErrorCategory.TIMEOUT: 504,
        }
        
        status_code = status_code_mapping.get(error.category, 500)
        
        response_data = {
            "error": {
                "code": error.code.value,
                "message": error.message,
                "category": error.category.value,
                "timestamp": error.timestamp.isoformat()
            }
        }
        
        # Include additional info for development/debugging
        if error.additional_info:
            response_data["error"]["details"] = error.additional_info
        
        return JSONResponse(
            status_code=status_code,
            content=response_data
        )
    
    @staticmethod
    def create_streaming_error_response(error: StandardizedError) -> str:
        """Create standardized streaming error response"""
        ErrorHandler.log_error(error)
        
        error_data = {
            "error": {
                "code": error.code.value,
                "message": error.message,
                "category": error.category.value,
                "timestamp": error.timestamp.isoformat()
            },
            "type": "error"
        }
        
        return f"data: {json.dumps(error_data)}\n\n"
