"""
Streaming Response Error Handling Utilities

This module provides robust error handling for streaming responses with graceful
error recovery and user-friendly error messages.
"""

import asyncio
import functools
import json
import logging
import traceback
from typing import Any, AsyncGenerator, Callable, Dict, List, Optional, TypeVar, Union

from fastapi import <PERSON><PERSON>PEx<PERSON>, Request
from fastapi.responses import JSONResponse, StreamingResponse

from app.utils.error_handler import (
    ErrorCategory,
    ErrorCode,
    ErrorHandler,
    ErrorSeverity,
    StandardizedError,
)
from app.utils.logger import chatbot_logger as logger

# Type variable for generic function return type
T = TypeVar("T")


class StreamingError(StandardizedError):
    """Streaming-specific errors"""
    
    def __init__(
        self,
        message: str,
        operation: Optional[str] = None,
        **kwargs
    ):
        additional_info = kwargs.get("additional_info", {})
        if operation:
            additional_info["operation"] = operation
        
        super().__init__(
            code=ErrorCode.INTERNAL_SERVER_ERROR,
            message=message,
            category=ErrorCategory.SYSTEM,
            severity=ErrorSeverity.HIGH,
            additional_info=additional_info,
            **kwargs
        )


async def safe_streaming_generator(
    generator_func: Callable[..., AsyncGenerator[str, None]],
    error_handler: Optional[Callable[[Exception], str]] = None,
    *args, **kwargs
) -> AsyncGenerator[str, None]:
    """
    Safely wrap a streaming generator with error handling
    
    Args:
        generator_func: Async generator function that yields streaming responses
        error_handler: Optional custom error handler function
        *args, **kwargs: Arguments to pass to generator_func
        
    Yields:
        Streaming response chunks with error handling
    """
    try:
        async for chunk in generator_func(*args, **kwargs):
            yield chunk
    except Exception as e:
        logger.error(f"Error in streaming generator: {str(e)}", exc_info=True)
        
        if error_handler:
            # Use custom error handler if provided
            error_response = error_handler(e)
        else:
            # Default error handling
            if isinstance(e, StandardizedError):
                error = e
            else:
                error = StreamingError(
                    message=f"Streaming error: {str(e)}",
                    operation="streaming_response",
                    original_exception=e
                )
            
            error_response = ErrorHandler.create_streaming_error_response(error)
        
        yield error_response


def handle_streaming_errors(
    error_message: str = "An error occurred during streaming",
    include_html_format: bool = True
):
    """
    Decorator to handle errors in streaming endpoints
    
    Args:
        error_message: User-friendly error message
        include_html_format: Whether to include HTML formatting in error message
        
    Returns:
        Decorated function with error handling
    """
    def decorator(func):
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            try:
                # Get the original generator
                generator = func(*args, **kwargs)
                
                # Create a safe generator with error handling
                return StreamingResponse(
                    safe_streaming_generator(
                        lambda: generator,
                        lambda e: create_error_chunk(e, error_message, include_html_format)
                    ),
                    media_type="text/event-stream"
                )
            except Exception as e:
                logger.error(f"Error in streaming endpoint: {str(e)}", exc_info=True)
                
                if isinstance(e, StandardizedError):
                    return ErrorHandler.create_error_response(e)
                else:
                    error = StreamingError(
                        message=error_message,
                        operation="streaming_endpoint",
                        original_exception=e
                    )
                    return ErrorHandler.create_error_response(error)
        
        return wrapper
    
    return decorator


def create_error_chunk(
    exception: Exception,
    error_message: str,
    include_html_format: bool = True
) -> str:
    """
    Create a formatted error chunk for streaming response
    
    Args:
        exception: The exception that occurred
        error_message: User-friendly error message
        include_html_format: Whether to include HTML formatting
        
    Returns:
        Formatted error chunk
    """
    if isinstance(exception, StandardizedError):
        error = exception
    else:
        error = StreamingError(
            message=error_message,
            operation="streaming_response",
            original_exception=exception
        )
    
    error_data = {
        "error": {
            "code": error.code.value,
            "message": error.message,
            "category": error.category.value,
            "timestamp": error.timestamp.isoformat()
        },
        "type": "error"
    }
    
    # Add HTML formatted error for UI display if requested
    if include_html_format:
        html_error = f"""
        <div class="error-message">
            <h3>Error</h3>
            <p>{error.message}</p>
            <p>Please try again or contact support if the issue persists.</p>
        </div>
        """
        error_data["html"] = html_error
    
    return f"data: {json.dumps(error_data)}\n\n"


class StreamingErrorRecovery:
    """
    Utilities for recovering from errors in streaming responses
    """
    
    @staticmethod
    async def with_partial_results(
        generator_func: Callable[..., AsyncGenerator[str, None]],
        fallback_message: str,
        *args, **kwargs
    ) -> AsyncGenerator[str, None]:
        """
        Execute streaming generator with partial results on error
        
        If an error occurs during streaming, this will return any partial
        results that were generated before the error, along with a fallback message.
        
        Args:
            generator_func: Async generator function
            fallback_message: Message to include if error occurs
            *args, **kwargs: Arguments to pass to generator_func
            
        Yields:
            Streaming response chunks with partial results on error
        """
        partial_results = []
        error_occurred = False
        
        try:
            async for chunk in generator_func(*args, **kwargs):
                partial_results.append(chunk)
                yield chunk
        except Exception as e:
            logger.error(f"Error in streaming generator: {str(e)}", exc_info=True)
            error_occurred = True
            
            if isinstance(e, StandardizedError):
                error = e
            else:
                error = StreamingError(
                    message=f"Streaming error: {str(e)}",
                    operation="streaming_response",
                    original_exception=e
                )
            
            # Create partial results message
            partial_results_message = {
                "partial_results": True,
                "message": fallback_message,
                "error": {
                    "code": error.code.value,
                    "message": error.message
                },
                "type": "partial_error"
            }
            
            yield f"data: {json.dumps(partial_results_message)}\n\n"
        
        finally:
            if error_occurred and not partial_results:
                # No partial results were generated, send a complete error
                error = StreamingError(
                    message="No results could be generated",
                    operation="streaming_response"
                )
                
                yield ErrorHandler.create_streaming_error_response(error)


async def stream_with_timeout(
    generator_func: Callable[..., AsyncGenerator[str, None]],
    timeout_seconds: float,
    timeout_message: str = "Operation timed out",
    *args, **kwargs
) -> AsyncGenerator[str, None]:
    """
    Execute streaming generator with timeout
    
    Args:
        generator_func: Async generator function
        timeout_seconds: Timeout in seconds
        timeout_message: Message to include if timeout occurs
        *args, **kwargs: Arguments to pass to generator_func
        
    Yields:
        Streaming response chunks with timeout handling
    """
    try:
        # Create a task for the generator
        gen_task = asyncio.create_task(anext(generator_func(*args, **kwargs).__aiter__()))
        
        while True:
            # Wait for next chunk with timeout
            done, pending = await asyncio.wait(
                [gen_task],
                timeout=timeout_seconds,
                return_when=asyncio.FIRST_COMPLETED
            )
            
            if not done:
                # Timeout occurred
                for task in pending:
                    task.cancel()
                
                error = StreamingError(
                    message=timeout_message,
                    operation="streaming_timeout",
                    code=ErrorCode.CONNECTION_TIMEOUT,
                    category=ErrorCategory.TIMEOUT
                )
                
                yield ErrorHandler.create_streaming_error_response(error)
                return
            
            # Get the result and yield it
            chunk = gen_task.result()
            yield chunk
            
            # Create a new task for the next chunk
            gen_task = asyncio.create_task(anext(generator_func(*args, **kwargs).__aiter__()))
    
    except StopAsyncIteration:
        # Generator is exhausted
        return
    except Exception as e:
        logger.error(f"Error in streaming with timeout: {str(e)}", exc_info=True)
        
        if isinstance(e, StandardizedError):
            error = e
        else:
            error = StreamingError(
                message=f"Streaming error: {str(e)}",
                operation="streaming_with_timeout",
                original_exception=e
            )
        
        yield ErrorHandler.create_streaming_error_response(error)
