import json
import time
import uuid
from datetime import datetime
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
import tiktoken
import asyncio
from functools import wraps

from app.core.config import settings
from app.utils.logger import generic_logger as logger

# Model pricing per million tokens (in USD)
MODEL_PRICING = {
    "claude-3-5-sonnet-20241022": {"input": 3.0 / 1_000_000, "output": 15.0 / 1_000_000},
    "sonar": {"input": 1.0 / 1_000_000, "output": 1.0 / 1_000_000},
    "gpt-4o": {"input": 2.5 / 1_000_000, "output": 10.0 / 1_000_000},
    "gpt-4o-mini": {"input": 0.15 / 1_000_000, "output": 0.60 / 1_000_000},
    "gpt-4.1-mini": {"input": 0.40 / 1_000_000, "output": 1.60 / 1_000_000},
    "o1-mini": {"input": 3.0 / 1_000_000, "output": 12.0 / 1_000_000},
}

# Map config names to pricing keys
MODEL_NAME_MAP = {
    settings.CLAUDE_MODEL_NAME: "claude-3-5-sonnet-20241022",
    settings.PERPLEXITY_MODEL_NAME: "sonar",
    settings.MODEL_NAME: "gpt-4o",
    settings.GPT_4O_MINI_MODEL_NAME: "gpt-4o-mini",
    settings.gpt_4_1_mini: "gpt-4.1-mini",
}

@dataclass
class ModelUsage:
    """Track individual model usage"""
    model_name: str
    function_name: str
    input_tokens: int
    output_tokens: int
    input_cost: float
    output_cost: float
    total_cost: float
    timestamp: str
    duration_ms: float
    reasoning_effort: Optional[str] = None
    is_cached: bool = False
    cached_cost_savings: float = 0.0

@dataclass
class RequestCostAnalysis:
    """Track complete request cost analysis"""
    request_id: str
    endpoint: str
    flow_type: str
    question: str
    customer_id: str
    account_id: str
    chat_id: str
    model_usages: List[ModelUsage]
    total_input_tokens: int
    total_output_tokens: int
    total_cost: float
    request_duration_ms: float
    timestamp: str
    web_search_calls: int = 0
    web_search_cost: float = 0.0

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for logging"""
        return {
            **asdict(self),
            'model_usages': [asdict(usage) for usage in self.model_usages]
        }

class ComprehensiveCostTracker:
    """Comprehensive cost tracking utility for the entire generic-v3 endpoint"""

    def __init__(self):
        self.current_request: Optional[RequestCostAnalysis] = None
        self.model_usages: List[ModelUsage] = []

    def start_request_tracking(
        self,
        endpoint: str,
        question: str,
        customer_id: str,
        account_id: str,
        chat_id: str
    ) -> str:
        """Start tracking a new request"""
        request_id = str(uuid.uuid4())

        self.current_request = RequestCostAnalysis(
            request_id=request_id,
            endpoint=endpoint,
            flow_type="",  # Will be set based on processing flow
            question=question,
            customer_id=customer_id,
            account_id=account_id,
            chat_id=chat_id,
            model_usages=[],
            total_input_tokens=0,
            total_output_tokens=0,
            total_cost=0.0,
            request_duration_ms=0.0,
            timestamp=datetime.now().isoformat()
        )
        self.model_usages = []

        logger.info(f"Started cost tracking for request {request_id}")
        return request_id

    def set_flow_type(self, flow_type: str):
        """Set the processing flow type"""
        if self.current_request:
            self.current_request.flow_type = flow_type
            logger.info(f"Set flow type: {flow_type}")

    def count_tokens(self, text: str, model_name: str) -> int:
        """Count tokens for given text and model"""
        try:
            # Map model name to tiktoken model name
            tiktoken_model = self._get_tiktoken_model(model_name)
            if tiktoken_model:
                encoding = tiktoken.encoding_for_model(tiktoken_model)
                return len(encoding.encode(str(text)))
            else:
                # Fallback: rough estimation (4 chars per token)
                return len(str(text)) // 4
        except Exception as e:
            logger.warning(f"Token counting failed for {model_name}: {e}")
            return len(str(text)) // 4

    def _get_tiktoken_model(self, model_name: str) -> Optional[str]:
        """Get tiktoken model name from our model name"""
        model_key = MODEL_NAME_MAP.get(model_name, model_name)

        if "gpt-4o" in model_key:
            return "gpt-4o"
        elif "gpt-4" in model_key:
            return "gpt-4"
        elif "gpt-3.5" in model_key:
            return "gpt-3.5-turbo"
        else:
            return None

    def track_model_usage(
        self,
        model_name: str,
        function_name: str,
        input_text: str,
        output_text: str,
        duration_ms: float,
        reasoning_effort: Optional[str] = None,
        actual_input_tokens: Optional[int] = None,
        actual_output_tokens: Optional[int] = None,
        is_cached: bool = False
    ) -> ModelUsage:
        """Track usage for a specific model call"""

        # Use actual tokens if provided, otherwise estimate
        input_tokens = actual_input_tokens or self.count_tokens(input_text, model_name)
        output_tokens = actual_output_tokens or self.count_tokens(output_text, model_name)

        # Get pricing
        model_key = MODEL_NAME_MAP.get(model_name, model_name.lower())
        pricing = MODEL_PRICING.get(model_key, {"input": 0, "output": 0})

        # Calculate costs
        input_cost = input_tokens * pricing["input"]
        output_cost = output_tokens * pricing["output"]

        # Calculate cached cost savings (50% discount for cached inputs)
        cached_cost_savings = 0.0
        if is_cached:
            cached_cost_savings = input_cost * 0.5
            input_cost = input_cost * 0.5

        total_cost = input_cost + output_cost

        usage = ModelUsage(
            model_name=model_name,
            function_name=function_name,
            input_tokens=input_tokens,
            output_tokens=output_tokens,
            input_cost=input_cost,
            output_cost=output_cost,
            total_cost=total_cost,
            timestamp=datetime.now().isoformat(),
            duration_ms=duration_ms,
            reasoning_effort=reasoning_effort,
            is_cached=is_cached,
            cached_cost_savings=cached_cost_savings
        )

        self.model_usages.append(usage)

        if self.current_request:
            self.current_request.model_usages.append(usage)
            self.current_request.total_input_tokens += input_tokens
            self.current_request.total_output_tokens += output_tokens
            self.current_request.total_cost += total_cost

        logger.debug(f"Tracked model usage: {model_name} - {function_name} - ${total_cost:.6f}")
        return usage

    def track_web_search_call(self, context_size: str = "medium"):
        """Track web search API call cost"""
        # Cost per call for web search (medium context)
        cost_per_call = 27.50 / 1000  # $27.50 per 1K calls

        if self.current_request:
            self.current_request.web_search_calls += 1
            self.current_request.web_search_cost += cost_per_call
            self.current_request.total_cost += cost_per_call

        logger.debug(f"Tracked web search call: ${cost_per_call:.6f}")

    def finalize_request_tracking(self, request_duration_ms: float):
        """Finalize request tracking and log results"""
        if not self.current_request:
            return

        self.current_request.request_duration_ms = request_duration_ms

        # Log comprehensive cost analysis
        self._log_cost_analysis()

        # Reset for next request
        current = self.current_request
        self.current_request = None
        self.model_usages = []

        return current

    def _log_cost_analysis(self):
        """Log detailed cost analysis"""
        if not self.current_request:
            return

        req = self.current_request

        # Create summary by model
        model_summary = {}
        for usage in req.model_usages:
            if usage.model_name not in model_summary:
                model_summary[usage.model_name] = {
                    "calls": 0,
                    "input_tokens": 0,
                    "output_tokens": 0,
                    "total_cost": 0.0,
                    "functions": [],
                    "cached_savings": 0.0
                }

            model_summary[usage.model_name]["calls"] += 1
            model_summary[usage.model_name]["input_tokens"] += usage.input_tokens
            model_summary[usage.model_name]["output_tokens"] += usage.output_tokens
            model_summary[usage.model_name]["total_cost"] += usage.total_cost
            model_summary[usage.model_name]["functions"].append(usage.function_name)
            model_summary[usage.model_name]["cached_savings"] += usage.cached_cost_savings

        # Log summary
        logger.info("="*100)
        logger.info("COMPREHENSIVE COST ANALYSIS REPORT")
        logger.info("="*100)
        logger.info(f"Request ID: {req.request_id}")
        logger.info(f"Endpoint: {req.endpoint}")
        logger.info(f"Flow Type: {req.flow_type}")
        logger.info(f"Customer ID: {req.customer_id}")
        logger.info(f"Account ID: {req.account_id}")
        logger.info(f"Chat ID: {req.chat_id}")
        logger.info(f"Question: {req.question[:100]}...")
        logger.info(f"Request Duration: {req.request_duration_ms:.2f}ms")
        logger.info("-"*100)

        # Log model-specific costs
        for model_name, summary in model_summary.items():
            logger.info(f"Model: {model_name}")
            logger.info(f"  API Calls: {summary['calls']}")
            logger.info(f"  Functions: {', '.join(set(summary['functions']))}")
            logger.info(f"  Input Tokens: {summary['input_tokens']:,}")
            logger.info(f"  Output Tokens: {summary['output_tokens']:,}")
            logger.info(f"  Total Cost: ${summary['total_cost']:.6f}")
            if summary['cached_savings'] > 0:
                logger.info(f"  Cached Savings: ${summary['cached_savings']:.6f}")
            logger.info("-"*50)

        # Log web search costs if any
        if req.web_search_calls > 0:
            logger.info(f"Web Search Calls: {req.web_search_calls}")
            logger.info(f"Web Search Cost: ${req.web_search_cost:.6f}")
            logger.info("-"*50)

        logger.info(f"TOTAL REQUEST COST: ${req.total_cost:.6f}")
        logger.info(f"TOTAL TOKENS: {req.total_input_tokens + req.total_output_tokens:,}")
        logger.info(f"TOTAL INPUT TOKENS: {req.total_input_tokens:,}")
        logger.info(f"TOTAL OUTPUT TOKENS: {req.total_output_tokens:,}")
        logger.info("="*100)

        # Log detailed breakdown for debugging
        logger.debug(f"Detailed cost breakdown: {json.dumps(req.to_dict(), indent=2)}")

# Global cost tracker instance
cost_tracker = ComprehensiveCostTracker()

def track_model_call(
    model_name: str,
    function_name: str,
    reasoning_effort: Optional[str] = None,
    is_cached: bool = False
):
    """Decorator to track model calls"""
    def decorator(func):
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            start_time = time.time()

            # Extract input text (usually first argument or 'question'/'prompt' in kwargs)
            input_text = ""
            if args:
                input_text = str(args[0])
            elif 'question' in kwargs:
                input_text = str(kwargs['question'])
            elif 'prompt' in kwargs:
                input_text = str(kwargs['prompt'])
            elif 'user_question' in kwargs:
                input_text = str(kwargs['user_question'])

            try:
                result = await func(*args, **kwargs)
                output_text = str(result) if result else ""

                duration_ms = (time.time() - start_time) * 1000

                # Track the usage
                cost_tracker.track_model_usage(
                    model_name=model_name,
                    function_name=function_name,
                    input_text=input_text,
                    output_text=output_text,
                    duration_ms=duration_ms,
                    reasoning_effort=reasoning_effort,
                    is_cached=is_cached
                )

                return result

            except Exception as e:
                duration_ms = (time.time() - start_time) * 1000

                # Track failed call
                cost_tracker.track_model_usage(
                    model_name=model_name,
                    function_name=function_name,
                    input_text=input_text,
                    output_text=f"ERROR: {str(e)}",
                    duration_ms=duration_ms,
                    reasoning_effort=reasoning_effort,
                    is_cached=is_cached
                )

                raise

        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            start_time = time.time()

            # Extract input text
            input_text = ""
            if args:
                input_text = str(args[0])
            elif 'question' in kwargs:
                input_text = str(kwargs['question'])
            elif 'prompt' in kwargs:
                input_text = str(kwargs['prompt'])
            elif 'user_question' in kwargs:
                input_text = str(kwargs['user_question'])

            try:
                result = func(*args, **kwargs)
                output_text = str(result) if result else ""

                duration_ms = (time.time() - start_time) * 1000

                # Track the usage
                cost_tracker.track_model_usage(
                    model_name=model_name,
                    function_name=function_name,
                    input_text=input_text,
                    output_text=output_text,
                    duration_ms=duration_ms,
                    reasoning_effort=reasoning_effort,
                    is_cached=is_cached
                )

                return result

            except Exception as e:
                duration_ms = (time.time() - start_time) * 1000

                # Track failed call
                cost_tracker.track_model_usage(
                    model_name=model_name,
                    function_name=function_name,
                    input_text=input_text,
                    output_text=f"ERROR: {str(e)}",
                    duration_ms=duration_ms,
                    reasoning_effort=reasoning_effort,
                    is_cached=is_cached
                )

                raise

        # Return appropriate wrapper based on function type
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper

    return decorator

def track_web_search():
    """Decorator to track web search calls"""
    def decorator(func):
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            try:
                result = await func(*args, **kwargs)
                cost_tracker.track_web_search_call()
                return result
            except Exception as e:
                cost_tracker.track_web_search_call()  # Still track the call even if it fails
                raise

        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            try:
                result = func(*args, **kwargs)
                cost_tracker.track_web_search_call()
                return result
            except Exception as e:
                cost_tracker.track_web_search_call()  # Still track the call even if it fails
                raise

        # Return appropriate wrapper based on function type
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper

    return decorator

# Helper functions for manual tracking
def track_openai_response(
    model_name: str,
    function_name: str,
    messages: List[Dict[str, Any]],
    response,
    duration_ms: float,
    reasoning_effort: Optional[str] = None
):
    """Helper function to track OpenAI API responses"""
    input_text = " ".join([msg.get("content", "") for msg in messages])
    output_text = ""

    if hasattr(response, 'choices') and response.choices:
        output_text = response.choices[0].message.content or ""

    actual_input_tokens = None
    actual_output_tokens = None

    if hasattr(response, 'usage'):
        actual_input_tokens = response.usage.prompt_tokens
        actual_output_tokens = response.usage.completion_tokens

    cost_tracker.track_model_usage(
        model_name=model_name,
        function_name=function_name,
        input_text=input_text,
        output_text=output_text,
        duration_ms=duration_ms,
        reasoning_effort=reasoning_effort,
        actual_input_tokens=actual_input_tokens,
        actual_output_tokens=actual_output_tokens
    )

def track_claude_response(
    model_name: str,
    function_name: str,
    messages: List[Dict[str, Any]],
    response,
    duration_ms: float
):
    """Helper function to track Claude API responses"""
    input_text = " ".join([msg.get("content", "") for msg in messages])
    output_text = ""

    if hasattr(response, 'content') and response.content:
        output_text = response.content[0].text if response.content else ""

    actual_input_tokens = None
    actual_output_tokens = None

    if hasattr(response, 'usage'):
        actual_input_tokens = response.usage.input_tokens
        actual_output_tokens = response.usage.output_tokens

    cost_tracker.track_model_usage(
        model_name=model_name,
        function_name=function_name,
        input_text=input_text,
        output_text=output_text,
        duration_ms=duration_ms,
        actual_input_tokens=actual_input_tokens,
        actual_output_tokens=actual_output_tokens
    )

# Example usage and testing function
def test_cost_tracking():
    """Test function to demonstrate cost tracking"""
    import time

    # Start tracking a test request
    request_id = cost_tracker.start_request_tracking(
        endpoint="/generic-v3",
        question="What is the current price of Bitcoin?",
        customer_id="test_customer",
        account_id="test_account",
        chat_id="test_chat"
    )

    cost_tracker.set_flow_type("test_flow")

    # Simulate some model calls
    cost_tracker.track_model_usage(
        model_name=settings.GPT_4O_MINI_MODEL_NAME,
        function_name="test_function_1",
        input_text="What is Bitcoin?",
        output_text="Bitcoin is a cryptocurrency...",
        duration_ms=1500.0
    )

    cost_tracker.track_model_usage(
        model_name=settings.PERPLEXITY_MODEL_NAME,
        function_name="test_function_2",
        input_text="Current Bitcoin price",
        output_text="Bitcoin is currently trading at $45,000...",
        duration_ms=2000.0
    )

    # Simulate web search
    cost_tracker.track_web_search_call()

    # Finalize tracking
    cost_tracker.finalize_request_tracking(5000.0)

    print("Cost tracking test completed. Check logs for detailed analysis.")

if __name__ == "__main__":
    test_cost_tracking()