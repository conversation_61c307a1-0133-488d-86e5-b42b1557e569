from fastapi.exceptions import HTTPException
from fastapi_pagination import Params
from fastapi_pagination.ext.sqlalchemy import paginate
from sqlalchemy import select, update
from sqlalchemy.ext.asyncio import AsyncSession

from app.db.chat_db import get_chat_collection, get_collection
from app.models.models import ChatR<PERSON>ord, Conversation
from app.schemas.chat_schemas import ChatModelSchema, ChatTypeEnum, ChatUpdateSchema
from app.utils.db_error_handler import (
    DatabaseErrorHandler,
    handle_database_errors,
    with_database_error_handling,
)
from app.utils.logger import chatbot_logger as logger


# mongo crud
async def create_or_insert_chat(chat_id: str, chat_data: dict):
    collection = await get_chat_collection()
    chat_record = await collection.update_one(
        {"chat_id": chat_id}, {"$push": {"qa_pairs": chat_data}}, upsert=True
    )

    if chat_record.modified_count > 0:
        logger.info(f"Chat record updated successfully: {chat_id}")
        return True
    else:
        logger.warning(f"Chat record not updated: {chat_id}")
        return False


async def clean_mongo():
    collection = await get_chat_collection()
    await collection.delete_many({})


async def update_feedback(chat_id: str, response_id: str, feedback: str):
    collection = await get_chat_collection()
    chat_record = await collection.update_one(
        {"chat_id": chat_id, "qa_pairs.response_id": response_id},
        {"$set": {"qa_pairs.$.feedback": feedback}},
    )

    if chat_record.modified_count > 0:
        logger.info(f"Feedback updated successfully: {chat_id}")
        return {"message": "Feedback updated successfully"}
    else:
        logger.warning(f"Feedback not updated: {chat_id}")
        raise HTTPException(status_code=404, detail="response not found")


async def update_chat_branches(chat_id: str, response_id: str, branches: list):
    collection = await get_chat_collection()
    chat_record = await collection.update_one(
        {"chat_id": chat_id, "qa_pairs.response_id": response_id},
        {"$set": {"qa_pairs.$.branches": branches}},
    )

    if chat_record.modified_count > 0:
        logger.info(f"Branches updated successfully: {chat_id}")
        return {"message": "Branches updated successfully"}
    else:
        logger.warning(f"Branches not updated: {chat_id}")
        raise HTTPException(status_code=404, detail="response not found")


async def delete_chat_in_mongo(chat_id: str):
    collection = await get_chat_collection()
    chat_record = await collection.delete_one({"chat_id": chat_id})
    if chat_record.deleted_count > 0:
        logger.info(f"Chat record deleted successfully: {chat_id}")
        return True
    else:
        logger.warning(f"Chat record not deleted: {chat_id}")
        return False


async def delete_chats_in_mongo(chat_ids: list):
    collection = await get_chat_collection()
    chat_record = await collection.delete_many({"chat_id": {"$in": chat_ids}})
    logger.info(f"Deleted {chat_record.deleted_count} chat records")
    return chat_record.deleted_count


async def get_chat_by_id(db: AsyncSession, chat_id: str):
    collection = await get_chat_collection()
    chat_record = await collection.find_one({"chat_id": chat_id})
    stmt = select(ChatRecord).where(ChatRecord.chat_id == chat_id)
    chat = await db.execute(stmt)
    chat = chat.scalar_one_or_none()
    if not chat_record:
        logger.warning(f"Chat record not found: {chat_id}")
        return None
    response = {
        "data": chat_record["qa_pairs"],
    }
    return response


async def get_chat_pair_by_response_id(chat_id: str, response_id: str):
    collection = await get_chat_collection()
    chat_pair = await collection.find_one(
        {"chat_id": chat_id, "qa_pairs.response_id": response_id},
        {"qa_pairs.$": 1},
    )
    if not chat_pair:
        return None
        logger.warning(f"Chat pair not found: {chat_id}")
    return chat_pair["qa_pairs"][0]


async def get_chat_by_id_new(db: AsyncSession, chat_id: str):
    """
    Fetches a chat record and compiles all branches into a flat structure.
    """
    collection = await get_chat_collection()
    chat_record = await collection.find_one({"chat_id": chat_id})
    stmt = select(ChatRecord).where(ChatRecord.chat_id == chat_id)
    chat = await db.execute(stmt)
    chat = chat.scalar_one_or_none()

    if not chat_record:
        logger.warning(f"Chat record not found: {chat_id}")
        return None

    async def gather_branches(qa_pairs, branches_dict):
        for pair in qa_pairs:
            branch_ids = pair.get("branches", [])
            for branch_id in branch_ids:
                if branch_id not in branches_dict:
                    branch_chat = await get_chat_record(db, branch_id)
                    if branch_chat:
                        chat_data = await get_chat_by_id(db, branch_id)
                        branch_chat_data = {
                            "chat_id": branch_id,
                            "active": branch_chat.active,
                            "qa_pairs": chat_data.get("data"),
                        }
                        branches_dict[branch_id] = branch_chat_data

                        await gather_branches(chat_data.get("data"), branches_dict)

    branches = {}
    await gather_branches(chat_record["qa_pairs"], branches)

    response = {
        "data": chat_record["qa_pairs"],
        "branches": branches,
    }

    return response


@with_database_error_handling("create_chat_record")
async def create_chat_record(db: AsyncSession, chat_record: ChatModelSchema):
    try:
        stmt = select(ChatRecord).where(ChatRecord.chat_id == chat_record.chat_id)
        result = await db.execute(stmt)
        chat = result.scalar_one_or_none()

        if not chat:
            chat_record = ChatRecord(**chat_record.dict())
            db.add(chat_record)
            await db.flush()  # Flush to get the ID
            await db.refresh(chat_record)
            logger.info(f"Chat record created successfully: {chat_record.chat_id}")
            return chat_record

        return chat
    except Exception as e:
        logger.error(f"Error creating chat record: {str(e)}")
        await db.rollback()
        raise DatabaseErrorHandler.classify_database_error(e)


@with_database_error_handling("create_conversation")
async def create_conversation(
    db: AsyncSession,
    chat_id: str,
    account_id: str,
    chat_type: str,
    name: str,
    agent_id: str = None,
    knowledge_id: str = None,
):
    """
    Creates a conversation record in the database.
    """
    try:
        stmt = select(Conversation).where(Conversation.chat_id == chat_id)
        result = await db.execute(stmt)
        conversation = result.scalar_one_or_none()

        if not conversation:
            conversation = Conversation(
                chat_id=chat_id,
                account_id=account_id,
                chat_type=chat_type,
                agent_id=agent_id,
                knowledge_id=knowledge_id,
                name=name,
            )
            db.add(conversation)
            await db.commit()  # Commit the transaction
            await db.refresh(conversation)
            logger.info(f"Conversation created successfully: {chat_id}")
            return conversation
        else:
            logger.warning(f"Conversation already exists: {chat_id}")
            return conversation
    except Exception as e:
        logger.error(f"Error creating conversation: {str(e)}")
        await db.rollback()
        raise DatabaseErrorHandler.classify_database_error(e)


async def get_conversation_by_chat_id(db: AsyncSession, chat_id: str):
    stmt = select(Conversation).where(
        Conversation.chat_id == chat_id, Conversation.deleted == False
    )
    result = await db.execute(stmt)
    conversation = result.scalar_one_or_none()
    return conversation


async def rename_conversation_by_chat_id(db: AsyncSession, chat_id: str, name: str):
    stmt = select(Conversation).where(Conversation.chat_id == chat_id)
    result = await db.execute(stmt)
    conversation = result.scalar_one_or_none()
    conversation.name = name
    await db.commit()
    await db.refresh(conversation)
    return conversation


async def remove_conversation_by_chat_id(db: AsyncSession, chat_id: str):
    stmt = select(Conversation).where(Conversation.chat_id == chat_id)
    result = await db.execute(stmt)
    conversation = result.scalar_one_or_none()
    conversation.deleted = True
    await db.commit()
    await db.refresh(conversation)


async def remove_conversations_by_chat_ids(db: AsyncSession, chat_ids: list):
    stmt = (
        update(Conversation)
        .where(Conversation.chat_id.in_(chat_ids))
        .values(deleted=True)
    )
    result = await db.execute(stmt)
    await db.commit()
    return result


async def get_conversations(
    db: AsyncSession,
    account_id: str,
    chat_type: str,
    agent_id: int,
    knowledge_id: int,
    params: Params,
):
    if chat_type == "AGENT":
        stmt = (
            select(Conversation)
            .where(
                Conversation.account_id == account_id,
                Conversation.chat_type == chat_type,
                Conversation.agent_id == agent_id,
                Conversation.deleted == False,
            )
            .order_by(Conversation.updated_at.desc())
        )
    elif chat_type == "MEDIA":
        stmt = (
            select(Conversation)
            .where(
                Conversation.account_id == account_id,
                Conversation.chat_type == chat_type,
                Conversation.knowledge_id == knowledge_id,
                Conversation.deleted == False,
            )
            .order_by(Conversation.updated_at.desc())
        )
    elif chat_type == "DRAWING":
        stmt = (
            select(Conversation)
            .where(
                Conversation.account_id == account_id,
                Conversation.chat_type == chat_type,
                Conversation.knowledge_id == knowledge_id,
                Conversation.deleted == False,
            )
            .order_by(Conversation.updated_at.desc())
        )
    else:
        stmt = (
            select(Conversation)
            .where(
                Conversation.account_id == account_id,
                Conversation.chat_type == chat_type,
                Conversation.deleted == False,
            )
            .order_by(Conversation.updated_at.desc())
        )
    paginated_result = await paginate(db, stmt, params)
    paginated_result.items = [
        {
            "chat_id": conversation.chat_id,
            "chat_name": conversation.name,
            "Created_At": conversation.created_at,
        }
        for conversation in paginated_result.items
    ]
    logger.info(f"Retrieved {len(paginated_result.items)} conversations")
    return paginated_result


async def update_chat_record(db: AsyncSession, chat_record: ChatUpdateSchema):
    result = await db.execute(
        select(ChatRecord).where(ChatRecord.chat_id == chat_record.chat_id)
    )
    chat = result.scalar_one_or_none()
    if not chat:
        logger.warning(f"Chat record not found: {chat_record.chat_id}")
        raise HTTPException(status_code=404, detail={"message": "Chat not found"})
    chat.name = chat_record.name
    await db.commit()
    await db.refresh(chat)
    return chat


async def get_chat_record(db: AsyncSession, chat_id: str):
    if chat_id:
        result = select(ChatRecord).where(ChatRecord.chat_id == chat_id)
        chat = await db.execute(result)
        chat = chat.scalar_one_or_none()
        return chat
    else:
        logger.warning(f"Chat record not found: {chat_id}")
        return None


async def delete_chat_record(db: AsyncSession, chat_id: str):
    result = await db.execute(select(ChatRecord).where(ChatRecord.chat_id == chat_id))
    chat = result.scalar_one_or_none()
    if not chat:
        logger.warning(f"Chat record not found: {chat_id}")
        return None
    chat.deleted = True
    await db.commit()
    await db.refresh(chat)
    return chat


async def delete_chat_records(db: AsyncSession, chat_ids: list):
    stmt = (
        update(ChatRecord).where(ChatRecord.chat_id.in_(chat_ids)).values(deleted=True)
    )
    result = await db.execute(stmt)
    await db.commit()
    return result


# async def get_all_normal_chats(db: AsyncSession, account_id: int):
#     result = await db.execute(
#         select(ChatRecord).where(
#             ChatRecord.account_id == account_id,
#             ChatRecord.chat_type == ChatTypeEnum.NORMAL.value,
#             ChatRecord.deleted == False,
#             ChatRecord.parent.is_(None)
#         )
#     )
#     results = result.scalars().all()
#     chats = []
#     # for result in results:
#     #     chat = await get_chat_by_id(db, result.chat_id)
#     #     chats.append(chat)
#     for result in results:
#         # chat = await get_chat_by_id(db, result.chat_id)
#         chats.append(
#             {
#                 "chat_id": result.chat_id,
#                 "chat_name": result.name,
#                 "Created_At": result.created_at,
#                 "chat_type": result.chat_type,
#             }
#         )
#     return chats


async def get_all_normal_chats(db: AsyncSession, account_id: int, params: Params):
    query = select(ChatRecord).where(
        ChatRecord.account_id == account_id,
        ChatRecord.chat_type == ChatTypeEnum.NORMAL.value,
        ChatRecord.deleted == False,
        ChatRecord.parent.is_(None),
    )
    paginated_result = await paginate(db, query, params)
    paginated_result.items = [
        {
            "chat_id": chat.chat_id,
            "chat_name": chat.name,
            "Created_At": chat.created_at,
            "chat_type": chat.chat_type,
        }
        for chat in paginated_result.items
    ]
    logger.info(f"Retrieved {len(paginated_result.items)} normal chats")
    return paginated_result


async def get_all_agent_chats(db: AsyncSession, account_id: int, agent_id: int):
    result = await db.execute(
        select(ChatRecord).where(
            ChatRecord.account_id == account_id,
            ChatRecord.agent_id == agent_id,
            ChatRecord.deleted == False,
        )
    )
    results = result.scalars().all()
    chats = []
    for result in results:
        # chat = await get_chat_by_id(db, result.chat_id)
        chats.append(
            {
                "chat_id": result.chat_id,
                "chat_name": result.name,
                "Created_At": result.created_at,
                "chat_type": result.chat_type,
            }
        )
    return chats


async def get_all_media_chats(db: AsyncSession, account_id: int, knowledge_id: int):
    result = await db.execute(
        select(ChatRecord).where(
            ChatRecord.account_id == account_id,
            ChatRecord.knowledge_id == knowledge_id,
            ChatRecord.deleted == False,
        )
    )
    results = result.scalars().all()
    chats = []
    for result in results:
        chat = await get_chat_by_id(db, result.chat_id)
        chats.append(chat)
    return chats


async def set_chat_as_active(db: AsyncSession, chat_id: str):
    # get all branch chats for its parent chat and set active status to false and make only the selected chat active
    stmt = select(ChatRecord).where(ChatRecord.chat_id == chat_id)
    result = await db.execute(stmt)
    chat = result.scalar_one_or_none()
    if not chat:
        logger.warning(f"Chat record not found: {chat_id}")
        return None
    # now get all chat where parent is chat.parent and set active to false for those chats
    stmt = select(ChatRecord).where(ChatRecord.parent == chat.parent)
    result = await db.execute(stmt)
    chats = result.scalars().all()
    for branch_chat in chats:
        branch_chat.active = False
        await db.commit()
        await db.refresh(branch_chat)
    chat.active = True
    await db.commit()
    await db.refresh(chat)
    return chat


async def set_chat_as_inactive(db: AsyncSession, chat_id: str):
    stmt = select(ChatRecord).where(ChatRecord.chat_id == chat_id)
    result = await db.execute(stmt)
    chat = result.scalar_one_or_none()
    if not chat:
        logger.warning(f"Chat record not found: {chat_id}")
        return None
    chat.active = False
    await db.commit()
    await db.refresh(chat)
    return chat


async def create_chat(chat_data):
    collection = await get_collection()
    await collection.insert_one(chat_data)
    return chat_data.get("id")

async def update_chat(id: str, **kwargs):
    collection = await get_collection()
    await collection.update_one({"id": id}, {"$set": kwargs})

async def get_chats_by_chat_id(chat_id: str):
    collection = await get_collection()
    cursor = collection.find({"chat_id": chat_id}, {"_id": 0})
    return [doc async for doc in cursor]


# async def get_chats_list(chat_id: str):
#     collection = await get_collection()
#     cursor = collection.find({"chat_id": chat_id}, {"_id": 0})
#     chat_list = []
#     first_chat = None
#     async for chat in cursor:
#         if chat.get('id') == chat_id:
#             first_chat = chat

#     return [doc async for doc in cursor]


async def give_feedback(id: str, feedback: int):
    collection = await get_collection()
    await collection.update_one({"id": id}, {"$set": {"feedback": feedback}})


async def get_chat_node(id: str):
    collection = await get_collection()
    return await collection.find_one({"id": id}, {"_id": 0})


# async def get_all_chat(chat_id: str):
#     collection = await get_collection()
#     cursor = collection.find({"chat_id": chat_id})
#     root = ''
#     async for doc in cursor:
#         if doc.get('id') == chat_id:
#             root = doc
#     lst = []
#     #
async def get_all_chat(chat_id: str):
    collection = await get_collection()
    cursor = collection.find({"chat_id": chat_id}, {"_id": 0})

    node_map = {}
    root_id = None
    active_path = []

    async for doc in cursor:
        node_map[doc["id"]] = doc
        if doc.get("id") == doc.get("parent"):
            root_id = doc["id"]

    def dfs(node_id):
        node = node_map.get(node_id)
        if not node:
            return
        active_path.append(node)
        children = node.get("children", [])
        active = node.get("active") or 0
        if children:
            dfs(children[active])

    dfs(root_id)
    return active_path[1:]
