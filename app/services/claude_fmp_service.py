import asyncio
import json
from typing import Any, Dict, List

import aiohttp
import anthropic

from app.core.config import settings
from app.core.llm_config import client
from app.prompts.fmp_prompt import fmp_prompt
from app.utils.fmp_utils import route_to_endpoint
from app.utils.logger import chatbot_logger as logger
from app.utils.comprehensive_cost_tracker import cost_tracker, track_openai_response

# from app.utils.selective_lru_cache import selective_lru_cache

model_name = settings.MODEL_NAME
FMP_API_KEY = settings.FMP_API_KEY


class AsyncTopicClassifier:
    def __init__(self, api_key: str):
        self.client = anthropic.Anthropic(api_key=api_key)

    async def ask_claude(self, prompt: str, max_tokens: int = 4096) -> str:
        """Asynchronously get response from <PERSON>"""
        import time
        start_time = time.time()

        try:
            messages = [{"role": "user", "content": prompt}]
            response = await asyncio.to_thread(
                self.client.messages.create,
                model=settings.CLAUDE_MODEL_NAME,
                max_tokens=max_tokens,
                messages=messages,
            )

            result = response.content[0].text.strip()

            # Track cost
            duration_ms = (time.time() - start_time) * 1000
            from app.utils.comprehensive_cost_tracker import track_claude_response
            track_claude_response(
                model_name=settings.CLAUDE_MODEL_NAME,
                function_name="ask_claude",
                messages=messages,
                response=response,
                duration_ms=duration_ms
            )

            return result
        except Exception as e:
            logger.error(f"Claude API error: {e}")
            return "Unable to generate summary"

    async def classify_query(
        self,
        prompt: str,
    ):
        import time
        start_time = time.time()

        try:
            from app.core.llm_config import call_groq_with_fallback

            messages = [{"role": "user", "content": prompt}]

            response, model_used = call_groq_with_fallback(
                messages=messages,
                temperature=0.2,
                max_tokens=4096,
                stream=False
            )
            response_result = response.choices[0].message.content

            # Track cost
            duration_ms = (time.time() - start_time) * 1000
            actual_model = "meta-llama/llama-4-scout-17b-16e-instruct" if model_used == "groq" else model_name

            # Create a mock response object for tracking if using Groq
            if model_used == "groq":
                from app.utils.comprehensive_cost_tracker import cost_tracker
                cost_tracker.track_model_usage(
                    model_name=actual_model,
                    function_name="classify_query",
                    input_text=prompt,
                    output_text=response_result,
                    duration_ms=duration_ms
                )
            else:
                track_openai_response(
                    model_name=actual_model,
                    function_name="classify_query",
                    messages=messages,
                    response=response,
                    duration_ms=duration_ms
                )

            return response_result
        except Exception as e:
            logger.error(f"API error: {e}")
            return "Unable to generate summary"

    async def classify_topic(self, query: str) -> str:
        """Asynchronously classify the topic of the query"""
        prompt = fmp_prompt(query)
        try:
            answer = await self.classify_query(prompt)  # , max_tokens=10
            answer = answer.lower().split(":")
            logger.info(f"Answer: {answer}")

            classification = answer[0].strip()
            symbol = "unknown"
            if len(answer) > 1:
                symbol = answer[1].strip()
            indicator_type = "unknown"
            if len(answer) > 2:
                indicator_type = answer[2].strip()
            logger.info(
                f"Classification: {classification}, Symbol: {symbol}, indicator_type: {indicator_type}"
            )

            valid_topics = [
                "news",
                "forex",
                "crypto",
                "technical_indicators",
                "company_info",
                "financial_statement",
                "statement_analysis",
                "historical_splits",
                "valuation",
                "price_target",
                "full_quote",
                "stock_price_change",
                "stock_up_down",
                "stock_consensus",
                "company_up_down",
                "earnings_transcript",
                "sec_filing",
                "intraday_commodities",
                "daily_commodities",
                "all_commodities",
                "market_risk_premium",
                "etf_holding",
                "mutual_funds",
                "market_performance",
                "merger_acquisition",
                "evn_social_gov",
                "senate",
                "insider_trading_symbol",
                "insider_trading_aggregated",
                "performers",
                "sales_by_segment",
                "sales_by_geo",
                "s_n_p_500",
                "nasdaq_100",
                "dow_jones",
                "sector_performance",
                "unknown",
            ]
            result = {
                "classification": (
                    classification if classification in valid_topics else "unknown"
                ),
                "symbol": (
                    symbol
                    if classification == "forex"
                    or "crypto"
                    or "technical_indicators"
                    or "company_info"
                    or "financial_statement"
                    or "statement_analysis"
                    or "historical_splits"
                    or "valuation"
                    or "price_target"
                    or "full_quote"
                    or "stock_price_change"
                    or "stock_up_down"
                    or "stock_consensus"
                    or "company_up_down"
                    or "earnings_transcript"
                    or "sec_filing"
                    or "intraday_commodities"
                    or "daily_commodities"
                    or "all_commodities"
                    or "market_risk_premium"
                    or "etf_holding"
                    or "mutual_funds"
                    or "market_performance"
                    or "merger_acquisition"
                    or "evn_social_gov"
                    or "senate"
                    or "insider_trading_symbol"
                    or "insider_trading_aggregated"
                    or "performers"
                    or "sales_by_segment"
                    or "sales_by_geo"
                    or "s_n_p_500"
                    or "nasdaq_100"
                    or "dow_jones"
                    else "unknown"
                ),
                "indicator_type": (
                    indicator_type
                    if classification == "technical_indicators" or "valuation"
                    else "unknown"
                ),
            }
            logger.info(f"Classification Result: {result}")
            return result

        except Exception as e:
            logger.error(f"Topic classification error: {e}")
            return "unknown"

    async def fetch_api_data(
        self, url: str, params: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Asynchronously fetch data from an API"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(url, params=params) as response:
                    if response.status == 200:
                        return await response.json()
                    else:
                        logger.error(
                            f"API request failed with status {response.status}"
                        )
                        return {}
        except Exception as e:
            logger.error(f"API fetch error: {e}")
            return {}

    async def generate_summary(self, query: str, response_data: Dict[str, Any]) -> str:
        """Generate a summary using Claude based on the API response"""
        summary_prompt = f"""You are a helpful assistant. 
        Provide a concise and informative summary based on the following:
        
        Query: {query}
        Context Data: {json.dumps(response_data)}
        
        - If no relevant data is available, explain why.
        - Summary should provide the relevant data with para and bullet points wherever needs.
        - Only use valid HTML syntax (e.g., <p>, <h1>) in your response.
        """

        return await self.ask_claude(summary_prompt)

    def filter_data(
        self, data: List[Dict[str, Any]], filter_key: str, filter_value: Any
    ) -> List[Dict[str, Any]]:
        # Filter the data
        filtered_data = [
            item for item in data if item.get(filter_key) == filter_value.upper()
        ]

        # Debug print to verify filtered data
        logger.info("Filtered Data:", filtered_data)

        return filtered_data

    async def process_query(self, query: str) -> Dict[str, Any]:
        """Asynchronously process a query from classification to summary"""
        try:
            # Classify the topic
            result = await self.classify_topic(query)
            symbol = result["symbol"].upper()
            topic = result["classification"]
            indicator_type = result["indicator_type"]
            logger.info(f"Classified topic: {topic}")

            # Initialize response data
            combined_response_data = []

            # Handle symbols with '/' (multiple symbols)
            if "/" in symbol:
                symbols = symbol.split("/")  # Split symbols
            else:
                symbols = [symbol]

            # Miscellaneous data for specific topics and params
            miscellaneous = {}
            if topic == "mutual_funds":
                mf_date = await self.fetch_api_data(
                    f"{settings.FMP_URL}/api/v4/mutual-fund-holdings/portfolio-date?symbol={symbol}&cik=**********&apikey={settings.FMP_API_KEY}"
                )
                mf_date = mf_date[0]

                miscellaneous = mf_date
                logger.info(f"Miscellaneous: {miscellaneous}")
            if topic == "earnings_transcript":
                ts_date = await self.fetch_api_data(
                    f"{settings.FMP_URL}/api/v4/earning_call_transcript?symbol={symbol}&cik=**********&apikey={settings.FMP_API_KEY}"
                )
                ts_date = ts_date[0]

                miscellaneous = {"ts_date": ts_date}
                logger.info(f"Miscellaneous: {miscellaneous}")

            for single_symbol in symbols:
                # Route to appropriate endpoint for each symbol
                routing_info = route_to_endpoint(
                    topic, query, single_symbol, indicator_type, miscellaneous
                )
                logger.info(f"Routing Info for {single_symbol}: {routing_info}")

                # Fetch API response for the symbol
                if topic != "unknown" and "url" in routing_info.keys():
                    response_data = await self.fetch_api_data(
                        routing_info["url"], {"apikey": FMP_API_KEY}
                    )
                    logger.info(f"API Response for {single_symbol}: {response_data}")

                    # Filter the response data
                    if topic == "etf_holding":
                        filtered_data = self.filter_data(
                            response_data, "asset", single_symbol
                        )
                    elif topic == "market_performance":
                        filtered_data = self.filter_data(
                            response_data, "symbol", single_symbol
                        )
                    elif topic == "financial_statement":
                        filtered_data = response_data[0]
                    else:
                        filtered_data = response_data

                    # Append filtered data to combined response
                    combined_response_data.extend(
                        filtered_data
                        if isinstance(filtered_data, list)
                        else [filtered_data]
                    )
            summary = None

            # Generate summary using the combined response data
            summary = await self.generate_summary(query, combined_response_data)

            chart_data = []
            if topic in ["crypto"]:
                response_data = await self.fetch_api_data(
                    f"{settings.FMP_URL}/api/v3/historical-price-full/{symbol}USD",
                    {"apikey": FMP_API_KEY},
                )
                chart_data = response_data["historical"][:7]

            api_data = {
                "topic": topic,
                "routing": routing_info,
                "data": combined_response_data,
                "summary": summary,
            }

            if chart_data:
                api_data["chart_data"] = chart_data

            return api_data

        except Exception as e:
            logger.error(f"Query processing error: {e}")
            return {
                "topic": "unknown",
                "routing": {"error": "Processing failed"},
                "data": {},
                "summary": "Unable to process the query.",
            }


# @selective_lru_cache(maxsize=100)
async def fmp_execute(query: str, chat_id: int) -> Dict[str, Any]:
    # Replace with your actual Anthropic API key
    api_key = settings.ANTHROPIC_API_KEY

    classifier = AsyncTopicClassifier(api_key)
    result = await classifier.process_query(query)
    logger.info(f"Result in fmp_execute: {result}, chat_id: {chat_id}")
    return result
