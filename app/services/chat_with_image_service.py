import base64
import os

import requests
from langchain.schema import AIMessage, HumanMessage, SystemMessage
from langchain.schema.output_parser import StrOutputParser
from langchain.schema.runnable import RunnablePassthrough
from langchain_openai import Chat<PERSON>penAI
from openai import OpenAI

from app.core.config import settings
from app.prompts.agent_prompts import chat_with_image_prompt
from app.utils.api_error_handler import handle_openai_error, with_circuit_breaker
from app.utils.comprehensive_cost_tracker import cost_tracker, track_openai_response
from app.utils.error_handler import FileOperationError, ExternalAPIError, ErrorSeverity
from app.utils.logger import chatbot_logger as logger

client = OpenAI(api_key=settings.OPENAI_API_KEY)


@with_circuit_breaker("image_download")
def download_image(image_url: str, account_id: int):
    try:
        # Validate image URL
        if not image_url or not image_url.startswith(('http://', 'https://')):
            raise FileOperationError(
                message="Invalid image URL provided",
                file_path=image_url,
                operation="download",
                additional_info={"account_id": account_id}
            )

        # Download image with timeout
        image_response = requests.get(image_url, timeout=30)
        image_response.raise_for_status()  # Raise exception for HTTP errors

        # Validate file extension
        extension = image_url.split(".")[-1].lower()
        if extension not in ["png", "jpg", "jpeg"]:
            raise FileOperationError(
                message=f"Unsupported image format: {extension}",
                file_path=image_url,
                operation="download",
                additional_info={
                    "account_id": account_id,
                    "extension": extension,
                    "supported_formats": ["png", "jpg", "jpeg"]
                }
            )

        # Create file path and ensure directory exists
        file_name = f"{account_id}_image.{extension}"
        file_path = os.path.join(settings.TMP_DIR, file_name)

        try:
            os.makedirs(settings.TMP_DIR, exist_ok=True)
        except OSError as e:
            raise FileOperationError(
                message=f"Failed to create temporary directory: {str(e)}",
                file_path=settings.TMP_DIR,
                operation="create_directory",
                original_exception=e,
                additional_info={"account_id": account_id}
            )

        # Write image file
        try:
            with open(file_path, "wb") as image_file:
                image_file.write(image_response.content)
        except IOError as e:
            raise FileOperationError(
                message=f"Failed to write image file: {str(e)}",
                file_path=file_path,
                operation="write_file",
                original_exception=e,
                additional_info={"account_id": account_id}
            )

        logger.info(f"Image downloaded successfully: {file_path}")
        return file_path, extension

    except requests.exceptions.Timeout:
        raise ExternalAPIError(
            service="image_download",
            message="Image download request timed out",
            status_code=408,
            severity=ErrorSeverity.MEDIUM,
            additional_info={"image_url": image_url, "account_id": account_id}
        )
    except requests.exceptions.HTTPError as e:
        raise ExternalAPIError(
            service="image_download",
            message=f"HTTP error downloading image: {str(e)}",
            status_code=e.response.status_code if e.response else 500,
            severity=ErrorSeverity.MEDIUM,
            additional_info={"image_url": image_url, "account_id": account_id}
        )
    except requests.exceptions.RequestException as e:
        raise ExternalAPIError(
            service="image_download",
            message=f"Request error downloading image: {str(e)}",
            status_code=500,
            additional_info={"image_url": image_url, "account_id": account_id}
        )
    except FileOperationError:
        # Re-raise file operation errors
        raise
    except Exception as e:
        raise FileOperationError(
            message=f"Unexpected error downloading image: {str(e)}",
            file_path=image_url,
            operation="download",
            original_exception=e,
            additional_info={"account_id": account_id}
        )


def encode_image(image_path):
    """
    Encode image file to base64 string with proper error handling

    Args:
        image_path: Path to the image file

    Returns:
        Base64 encoded string of the image

    Raises:
        FileOperationError: If image encoding fails
    """
    try:
        # Validate image path
        if not image_path or not os.path.exists(image_path):
            raise FileOperationError(
                message="Image file not found",
                file_path=image_path,
                operation="encode",
                additional_info={"exists": os.path.exists(image_path) if image_path else False}
            )

        # Check file size (limit to 20MB)
        file_size = os.path.getsize(image_path)
        max_size = 20 * 1024 * 1024  # 20MB
        if file_size > max_size:
            raise FileOperationError(
                message=f"Image file too large: {file_size} bytes (max: {max_size} bytes)",
                file_path=image_path,
                operation="encode",
                additional_info={"file_size": file_size, "max_size": max_size}
            )

        # Encode image
        with open(image_path, "rb") as image_file:
            encoded_data = base64.b64encode(image_file.read()).decode("utf-8")
            logger.info(f"Image encoded successfully: {image_path}")
            return encoded_data

    except FileOperationError:
        # Re-raise file operation errors
        raise
    except IOError as e:
        raise FileOperationError(
            message=f"Failed to read image file: {str(e)}",
            file_path=image_path,
            operation="encode",
            original_exception=e
        )
    except Exception as e:
        raise FileOperationError(
            message=f"Unexpected error encoding image: {str(e)}",
            file_path=image_path,
            operation="encode",
            original_exception=e
        )


def remove_image(image_path):
    """
    Remove image file with proper error handling

    Args:
        image_path: Path to the image file to remove

    Raises:
        FileOperationError: If image removal fails
    """
    try:
        if not image_path:
            logger.warning("No image path provided for removal")
            return

        if os.path.exists(image_path):
            os.remove(image_path)
            logger.info(f"Image removed successfully: {image_path}")
        else:
            logger.warning(f"Image file not found for removal: {image_path}")

    except OSError as e:
        raise FileOperationError(
            message=f"Failed to remove image file: {str(e)}",
            file_path=image_path,
            operation="remove",
            original_exception=e
        )
    except Exception as e:
        raise FileOperationError(
            message=f"Unexpected error removing image: {str(e)}",
            file_path=image_path,
            operation="remove",
            original_exception=e
        )


def chat_with_image(image_url, user_input, account_id):
    import time
    start_time = time.time()

    try:
        image_path, extension = download_image(image_url, account_id)

        base64_image = encode_image(image_path)

        messages = [
            {
                "role": "system",
                "content": "You are an image analysis assistant. You need to analyse the provided image and answer the user's question. Respond only in html rich text with tags like <strong>, <p>, <ul>, <li> etc. This should be directly rendered in the UI with innerHTML.",
            },
            {
                "role": "user",
                "content": [
                    {"type": "text", "text": user_input},
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": f"data:image/{extension};base64,{base64_image}"
                        },
                    },
                ],
            },
        ]

        response = client.chat.completions.create(
            model=settings.GPT_4O_MINI_MODEL_NAME,
            messages=messages,
            temperature=0.0,
        )

        # Track cost
        duration_ms = (time.time() - start_time) * 1000
        track_openai_response(
            model_name=settings.GPT_4O_MINI_MODEL_NAME,
            function_name="chat_with_image",
            messages=messages,
            response=response,
            duration_ms=duration_ms
        )

        return (
            response.choices[0]
            .message.content.replace("```html", "")
            .replace("```", "")
        )

    finally:
        remove_image(image_path)


def chat_with_image_generic(images, user_input, account_id, history=[], chart_data=[]):
    import time
    start_time = time.time()

    try:
        images_details = []
        image_paths = []
        messages = [
            {
                "role": "system",
                "content": f"""You are a helpful assistant that can analyze images and their chartdata and answer to the users question about their contents. If the question is related to candlestick patterns, bars, datetime(timestamp) or price, provide the exact answer from the chartdata, this chart data is about candlesticks what we are seeing inn the image. If the user is not asking for details, keep the response concise.

                Here is the chartdata, this chartdata is about candles OHLCV data what we have in image, starts from the first candle to the last candle in the image: {chart_data}

                Always provide responses in **strong HTML rich text** format with **paragraphs and bullet points** when needed, so it can be directly rendered in the UI using `innerHTML`.

                ---

                **Analysis Logic:**
                1. **If the question requires real-time data (e.g., live market prices, latest stock movements, or external updates), response fomrat should be only this without html tags `"realtime"`.**
                2. **If the question is directly related to the provided image and chartdata(candle, candlestick, price, etc) and can be answered by analyzing it, give a detailed explanation in HTML format.**

                **Boundary Framework**
                # Non-Advisory Role
                - "Analyze the chart patterns and explain them."
                - "I can explain options but cannot advise on what you should do."
                - "Let me outline possibilities - final decisions require human consultation."

                # Prohibited Actions
                Never:
                - Endorse specific investments/products.
                - State preferences (e.g., "Stock X is better than Y").
                - Use imperative language ("You must...", "We recommend...").
                - Advise users to buy or sell. **Only provide explanations.**

                response format:
                - **If the question requires real-time data (latest, today's, current, live etc), response fomrat should be only this without html tags "realtime"**. ex: realtime
                - If the question is directly related to the provided image and can be answered by analyzing it, give a detailed explanation in HTML format.

                example:
                response1: realtime
                response2: <p>model's answer must be in a HTML markup format with valid html tags with para and bullet points if needed format.</p>

                """,
            }
        ]
        for pair in history:
            messages.append(
                {
                    "role": "user",
                    "content": pair[0],
                }
            )
            messages.append(
                {
                    "role": "assistant",
                    "content": pair[1],
                }
            )
        content = [
            {"type": "text", "text": user_input},
        ]
        messages.append(
            {"role": "user", "content": content},
        )

        for image in images:
            image_path, extension = download_image(image, account_id)
            image_paths.append(image_path)
            images_details.append(
                {
                    "image_url": f"data:image/{extension};base64,{encode_image(image_path)}",
                    "image_path": image_path,
                }
            )

        for image_detail in images_details:
            content.append(
                {
                    "type": "image_url",
                    "image_url": {"url": image_detail["image_url"]},
                }
            )

        response = client.chat.completions.create(
            model=settings.GPT_4O_MINI_MODEL_NAME,
            messages=messages,
            temperature=0.0,
        )

        # Track cost
        duration_ms = (time.time() - start_time) * 1000
        track_openai_response(
            model_name=settings.GPT_4O_MINI_MODEL_NAME,
            function_name="chat_with_image_generic",
            messages=messages,
            response=response,
            duration_ms=duration_ms
        )

        return (
            response.choices[0]
            .message.content.replace("```html", "")
            .replace("```", "")
        )

    finally:
        for image_path in image_paths:
            if image_path:
                remove_image(image_path)


async def chat_with_image_generic_stream(images, user_input, account_id, history=[]):
    try:
        images_details = []
        image_paths = []
        messages = [
            {
                "role": "system",
                "content": chat_with_image_prompt,
            }
        ]

        # Add history to messages
        for pair in history:
            messages.append({"role": "user", "content": pair[0]})
            messages.append({"role": "assistant", "content": pair[1]})

        # Prepare images
        for image in images:
            image_path, extension = download_image(image, account_id)
            image_paths.append(image_path)
            images_details.append(
                {
                    "image_url": f"data:image/{extension};base64,{encode_image(image_path)}",
                    "image_path": image_path,
                }
            )

        # Create message content with user input and images
        content = [{"type": "text", "text": user_input}]
        for image_detail in images_details:
            content.append(
                {
                    "type": "image_url",
                    "image_url": {"url": image_detail["image_url"]},
                }
            )

        messages.append({"role": "user", "content": content})

        # Stream the response
        response = client.chat.completions.create(
            model=settings.GPT_4O_MINI_MODEL_NAME,
            messages=messages,
            stream=True,
            temperature=0.0,
        )

        # Yield each chunk of the response
        # Yield each chunk of the response
        for chunk in response:
            if chunk.choices[0].delta.content:
                yield chunk.choices[0].delta.content.replace("```html", "").replace(
                    "```", ""
                )
    finally:
        # Clean up image files
        for image_path in image_paths:
            if image_path:
                remove_image(image_path)


async def chat_with_image_generic_stream_v3(images, user_input, account_id, history=[]):
    import time
    start_time = time.time()
    full_response = ""

    try:
        images_details = []
        image_paths = []

        # Build messages list
        messages = [SystemMessage(content=chat_with_image_prompt)]

        # Add history to messages
        for pair in history:
            messages.append(HumanMessage(content=pair[0]))
            messages.append(AIMessage(content=pair[1]))

        # Prepare images
        for image in images:
            image_path, extension = download_image(image, account_id)
            image_paths.append(image_path)
            images_details.append(
                {
                    "image_url": f"data:image/{extension};base64,{encode_image(image_path)}",
                    "image_path": image_path,
                }
            )

        # Create message content with user input and images
        content = [{"type": "text", "text": user_input}]
        for image_detail in images_details:
            content.append(
                {
                    "type": "image_url",
                    "image_url": {"url": image_detail["image_url"]},
                }
            )

        messages.append(HumanMessage(content=content))

        # Create LangChain components
        llm = ChatOpenAI(
            model=settings.GPT_4O_MINI_MODEL_NAME, temperature=0.0, streaming=True
        )

        # Create chain
        chain = RunnablePassthrough() | llm | StrOutputParser()

        # Stream the response
        async for chunk in chain.astream(messages):
            if chunk:
                cleaned_chunk = chunk.replace("```html", "").replace("```", "")
                full_response += cleaned_chunk
                yield cleaned_chunk

        # Track cost after streaming is complete
        duration_ms = (time.time() - start_time) * 1000
        input_text = f"{chat_with_image_prompt}\n{user_input}"
        cost_tracker.track_model_usage(
            model_name=settings.GPT_4O_MINI_MODEL_NAME,
            function_name="chat_with_image_generic_stream_v3",
            input_text=input_text,
            output_text=full_response,
            duration_ms=duration_ms
        )

    finally:
        # Clean up image files
        for image_path in image_paths:
            if image_path:
                remove_image(image_path)


def chat_with_image_generic_v3(
    images, user_input, account_id, history=[], chart_data=[]
):
    import time
    start_time = time.time()

    try:
        images_details = []
        image_paths = []

        # Create the system prompt
        system_prompt = f"""You are a helpful assistant that can analyze images and their chartdata and answer to the users question about their contents. If the question is related to candlestick patterns, bars, datetime(timestamp) or price, provide the exact answer from the chartdata, this chart data is about candlesticks what we are seeing inn the image. If the user is not asking for details, keep the response concise.

                Here is the chartdata, this chartdata is about candles OHLCV data what we have in image, starts from the first candle to the last candle in the image: {chart_data}

                Always provide responses in **strong HTML rich text** format with **paragraphs and bullet points** when needed, so it can be directly rendered in the UI using `innerHTML`.

                ---

                **Analysis Logic:**
                1. **If the question requires real-time data (e.g., live market prices, latest stock movements, or external updates), response format should be only this without html tags `"realtime"`.**
                2. **If the question is directly related to the provided chartdata(candle, candlestick, bar, price, levels etc) and can be answered by analyzing it, give a detailed explanation in HTML format.**

                **Boundary Framework**
                # Non-Advisory Role
                - "Analyze the chart patterns(Chartdata) and explain them."
                - "I can explain options but cannot advise on what you should do."
                - "Let me outline possibilities - final decisions require human consultation."

                # Prohibited Actions
                Never:
                - Endorse specific investments/products.
                - State preferences (e.g., "Stock X is better than Y").
                - Use imperative language ("You must...", "We recommend...").
                - Advise users to buy or sell. **Only provide explanations.**

                # Response format:
                - **If the question requires real-time data (latest, today's, current, live etc), response fomrat should be only this without html tags "realtime"**. ex: realtime
                - If the question is directly related to the provided image and can be answered by analyzing it, give a detailed explanation in HTML format.

                example:
                response1: realtime
                response2: <p>model's answer must be in a HTML markup format with valid html tags with para and bullet points if needed format.</p>

                """

        # Build messages list
        messages = [SystemMessage(content=system_prompt)]

        # Add history
        for pair in history:
            messages.append(HumanMessage(content=pair[0]))
            messages.append(AIMessage(content=pair[1]))

        # Process images
        content = [{"type": "text", "text": user_input}]

        for image in images:
            image_path, extension = download_image(image, account_id)
            image_paths.append(image_path)
            images_details.append(
                {
                    "image_url": f"data:image/{extension};base64,{encode_image(image_path)}",
                    "image_path": image_path,
                }
            )

        for image_detail in images_details:
            content.append(
                {
                    "type": "image_url",
                    "image_url": {"url": image_detail["image_url"]},
                }
            )

        messages.append(HumanMessage(content=content))

        # Create LangChain components
        llm = ChatOpenAI(model=settings.GPT_4O_MINI_MODEL_NAME, temperature=0.0)
        # Create chain
        chain = RunnablePassthrough() | llm | StrOutputParser()
        # Execute chain
        response = chain.invoke(messages)

        # Track cost
        duration_ms = (time.time() - start_time) * 1000
        input_text = f"{system_prompt}\n{user_input}"
        cost_tracker.track_model_usage(
            model_name=settings.GPT_4O_MINI_MODEL_NAME,
            function_name="chat_with_image_generic_v3",
            input_text=input_text,
            output_text=response,
            duration_ms=duration_ms
        )

        return response.replace("```html", "").replace("```", "")

    finally:
        for image_path in image_paths:
            if image_path:
                remove_image(image_path)


def chat_with_stored_image_generic_v3(images, user_input, account_id, history=[]):
    import time
    start_time = time.time()

    try:
        images_details = []
        image_paths = []

        STRUCTURED_PROMPT = f"""
        You are a helpful assistant that provides accurate, insightful answers to user questions based on image analysis and prior conversation context. Your responses are directly rendered in a chat interface using innerHTML, so only return well-formatted HTML.

        ### Context:
        - Previous conversation: {history}
        - User's current question: {user_input}

        ### Response Format:
        <answer>

        ### IMPORTANT CONDITIONS — If **any** of the following are true, respond exactly as:
        <p>intent_fallback_intent</p>

        1. The answer is not present or not relevant to the provided image.
        2. The user's question relates to markets, events, or general real-time information (e.g., news, stocks, politics).
        3. The question contains **time-specific references** such as:
           - today, tomorrow, yesterday, this month, last week, next year, etc.
        4. The user's message matches any of the **common fallback intents**, including greetings or general chatbot interactions.

        **Fallback Intent List with Examples:**
        <ul>
            <li>greet — Examples: hello, hi, hii, hyy, hey</li>
            <li>goodbye — Examples: bye, see you, goodbye</li>
            <li>thank_you — Examples: thanks, thank you, thx</li>
            <li>help — Examples: help me, I need support</li>
            <li>affirm — Examples: yes, yeah, okay, sure</li>
            <li>deny — Examples: no, nope, not really</li>
            <li>restart — Examples: restart, start over, reset</li>
            <li>bot_identity — Examples: who are you?, are you a bot?</li>
            <li>report_issue — Examples: I have a problem, report error</li>
            <li>check_status — Examples: what's the status?, update me</li>
            <li>cancel_request — Examples: cancel my request</li>
            <li>escalate_issue — Examples: talk to human, escalate this</li>
        </ul>

        ### HTML Output Rules:
        - Return the answer as valid HTML using tags like <p>, <ul>, <li>, <strong>, etc.
        - Do NOT include markdown, plain text, or JSON.
        - Output should be clean, minimal, and ready to render using innerHTML.

        Begin your response below:
        <answer>
        """

        # Build messages list
        messages = [SystemMessage(content=STRUCTURED_PROMPT)]

        # Add history
        for pair in history:
            messages.append(HumanMessage(content=pair[0]))
            messages.append(AIMessage(content=pair[1]))

        # Process images
        content = [{"type": "text", "text": user_input}]

        for image in images:
            image_path, extension = download_image(image, account_id)
            image_paths.append(image_path)
            images_details.append(
                {
                    "image_url": f"data:image/{extension};base64,{encode_image(image_path)}",
                    "image_path": image_path,
                }
            )

        for image_detail in images_details:
            content.append(
                {
                    "type": "image_url",
                    "image_url": {"url": image_detail["image_url"]},
                }
            )

        messages.append(HumanMessage(content=content))

        # Create LangChain components
        llm = ChatOpenAI(model=settings.MODEL_NAME, temperature=0.0)
        # Create chain
        chain = RunnablePassthrough() | llm | StrOutputParser()
        # Execute chain
        response = chain.invoke(messages)

        # Track cost
        duration_ms = (time.time() - start_time) * 1000
        input_text = f"STRUCTURED_PROMPT\n{user_input}"
        cost_tracker.track_model_usage(
            model_name=settings.MODEL_NAME,
            function_name="chat_with_stored_image_generic_v3",
            input_text=input_text,
            output_text=response,
            duration_ms=duration_ms
        )

        if "intent_fallback" in response:
            return ""
        else:
            return response.replace("```html", "").replace("```", "")

    finally:
        for image_path in image_paths:
            if image_path:
                remove_image(image_path)


async def chat_with_multiple_images(images, user_input):
    import time
    start_time = time.time()

    client = OpenAI(api_key=settings.OPENAI_API_KEY)

    message_content = [
        {
            "type": "text",
            "text": user_input,
        },
    ]
    for image in images:
        message_content.append(
            {
                "type": "image_url",
                "image_url": {
                    "url": image,
                },
            }
        )

    messages = [
        {
            "role": "user",
            "content": message_content,
        }
    ]

    response = client.chat.completions.create(
        model=settings.MODEL_NAME,
        messages=messages,
        max_tokens=300,
    )

    # Track cost
    duration_ms = (time.time() - start_time) * 1000
    track_openai_response(
        model_name=settings.MODEL_NAME,
        function_name="chat_with_multiple_images",
        messages=messages,
        response=response,
        duration_ms=duration_ms
    )

    # print(response.choices[0])
    logger.info(f"Chat with image response: {response.choices[0].message.content}")
    return response.choices[0].message.content
