from enum import Enum


class ToneEnum(str, Enum):
    default = "default"
    creative = "creative"
    inspiring = "inspiring"
    casual = "casual"
    confident = "confident"
    authoritative = "authoritative"
    friendly = "friendly"
    empathetic = "empathetic"
    optimistic = "optimistic"
    formal = "formal"
    serious = "serious"
    warm = "warm"
    humorous = "humorous"
    informal = "informal"
    clinical = "clinical"
    playful = "playful"
    emotional = "emotional"
    sympathetic = "sympathetic"
    tentative = "tentative"
    cold = "cold"
    cynical = "cynical"
    ironic = "ironic"
    sarcastic = "sarcastic"
    pessimistic = "pessimistic"


class LLMEnum(str, Enum):
    openai_4o = "openai_4o"
    openai_4o_mini = "openai_4o_mini"
    groq = "groq"
    llama3_1_70b_versatile = "llama-3.1-70b-versatile"
    llama2_9b_it = "gemma2-9b-it"


class KnowledgeTypeEnum(str, Enum):
    PDF = "PDF"
    CSV = "CSV"
    TEXT = "TEXT"
    DOCX = "DOCX"
    WEBLINK = "WEBLINK"
    YOUTUBE = "YOUTUBE"
    IMAGE = "IMAGE"


class FolderItemTypeEnum(str, Enum):
    TEAM = "TEAM"
    AGENT = "AGENT"
    KNOWLEDGE = "KNOWLEDGE"
    CHAT = "CHAT"


class ChatTypeEnum(str, Enum):
    NORMAL = "NORMAL"
    AGENT = "AGENT"
    DRAWING = "DRAWING"
    MEDIA = "MEDIA"
    OTHER = "OTHER"


class AvatarTypeEnum(str, Enum):
    IMAGE = "IMAGE"
    EMOJI = "EMOJI"


class LanguageEnum(str, Enum):
    DEFAULT = "Default"
    ENGLISH_US = "English (US)"
    ENGLISH_UK = "English (UK)"
    DEUTSCH = "Deutsch"
    ESPANOL = "Español"


class ToolEnum(str, Enum):
    WEB_SEARCH = "Web Search"
    WEB_SCRAPER = "Web Scraper"
    VISUAL_REFERENCE = "Visual Reference"


class LinksEnum(str, Enum):
    YOUTUBE = "YOUTUBE"
    WEBLINK = "WEBLINK"


class CommandModeEnum(str, Enum):
    DEFAULT = "Default"
    PLAN_AND_EXECUTE = "Plan and execute"


class PublicAgentModulusEnum(str, Enum):
    CHAT_ONLY = "Chat Only"
    CHAT_AND_TEMPLATES = "Chat and Templates"


class PublicAgentThemeEnum(str, Enum):
    AUTO = "Auto"
    LIGHT = "Light"
    DARK = "Dark"
