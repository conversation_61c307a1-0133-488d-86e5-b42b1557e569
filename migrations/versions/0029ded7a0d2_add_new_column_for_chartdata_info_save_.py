"""add new column for chartdata info save_image table

Revision ID: 0029ded7a0d2
Revises: 8b5a344cb38e
Create Date: 2025-06-02 14:47:42.295250

"""
from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "0029ded7a0d2"
down_revision: Union[str, None] = "8b5a344cb38e"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("scan_image", sa.Column("chartdata", sa.JSON(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("scan_image", "chartdata")
    # ### end Alembic commands ###
