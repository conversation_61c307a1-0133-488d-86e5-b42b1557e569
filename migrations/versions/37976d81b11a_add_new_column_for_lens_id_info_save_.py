"""add new column for lens_id info save_image table

Revision ID: 37976d81b11a
Revises: e8507f157658
Create Date: 2025-06-04 11:17:03.736220

"""
from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "37976d81b11a"
down_revision: Union[str, None] = "0029ded7a0d2"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "scan_image", sa.Column("lens_id", sa.String(length=100), nullable=False)
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("scan_image", "lens_id")
    # ### end Alembic commands ###
