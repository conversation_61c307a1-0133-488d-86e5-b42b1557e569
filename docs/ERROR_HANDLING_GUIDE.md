# Comprehensive Error Handling Framework

This document provides a complete guide to the standardized error handling framework implemented across the microservice chat backend.

## Overview

The error handling framework provides:
- **Standardized Error Types**: Consistent error classification and formatting
- **Circuit Breaker Pattern**: Prevents cascading failures in external service calls
- **Comprehensive Monitoring**: Real-time error tracking and alerting
- **Streaming Error Handling**: Graceful error recovery in streaming responses
- **Validation Framework**: Robust input validation with detailed error messages
- **Database Error Management**: Transaction-safe database operations

## Core Components

### 1. Standardized Error Types

#### Base Error Class
```python
from app.utils.error_handler import StandardizedError, ErrorCode, ErrorCategory, ErrorSeverity

error = StandardizedError(
    code=ErrorCode.INVALID_INPUT,
    message="User-friendly error message",
    category=ErrorCategory.VALIDATION,
    severity=ErrorSeverity.MEDIUM,
    additional_info={"field": "email"}
)
```

#### Specific Error Types
- **ValidationError**: Input validation failures
- **ExternalAPIError**: External service call failures
- **DatabaseError**: Database operation failures
- **FileOperationError**: File system operation failures
- **BusinessLogicError**: Business rule violations
- **StreamingError**: Streaming response failures

### 2. Error Response Format

All errors follow a consistent JSON format:
```json
{
  "error": {
    "code": "ERR_1001",
    "message": "User-friendly error message",
    "category": "validation",
    "timestamp": "2024-01-15T10:30:00Z",
    "details": {
      "field": "email",
      "additional_context": "..."
    }
  }
}
```

### 3. Circuit Breaker Pattern

Prevents cascading failures by monitoring service health:

```python
from app.utils.api_error_handler import with_circuit_breaker

@with_circuit_breaker("external_service")
async def call_external_service(data):
    # Service call implementation
    pass
```

**Circuit States:**
- **CLOSED**: Normal operation, requests flow through
- **OPEN**: Service failing, requests blocked
- **HALF_OPEN**: Testing recovery, limited requests allowed

### 4. Error Monitoring System

Real-time error tracking and alerting:

```python
from app.utils.error_monitoring import monitor_error, get_health_status

# Record error for monitoring
monitor_error(error, context={"user_id": "123", "endpoint": "/api/chat"})

# Get system health status
health = get_health_status()
```

**Monitoring Features:**
- Error frequency tracking
- Pattern detection
- Automatic alerting
- Health status calculation
- Metrics aggregation

## Implementation Guide

### 1. API Endpoints

#### Basic Error Handling
```python
from fastapi import APIRouter, Request
from app.utils.error_handler import ErrorHandler, ValidationError

@router.post("/api/endpoint")
async def endpoint(request: Request, data: RequestModel):
    try:
        # Endpoint logic
        return {"result": "success"}
    except ValidationError as e:
        return ErrorHandler.create_error_response(e, request)
    except Exception as e:
        error = StandardizedError(
            code=ErrorCode.INTERNAL_SERVER_ERROR,
            message="Internal server error",
            category=ErrorCategory.SYSTEM,
            severity=ErrorSeverity.HIGH,
            original_exception=e
        )
        return ErrorHandler.create_error_response(error, request)
```

#### Streaming Endpoints
```python
from app.utils.streaming_error_handler import handle_streaming_errors, safe_streaming_generator

@router.post("/api/stream")
@handle_streaming_errors("Streaming error occurred")
async def streaming_endpoint(data: RequestModel):
    async def stream_generator():
        try:
            for item in process_data(data):
                yield f"data: {json.dumps(item)}\n\n"
        except Exception as e:
            raise StreamingError(
                message="Processing failed",
                operation="data_processing",
                original_exception=e
            )
    
    return StreamingResponse(
        safe_streaming_generator(stream_generator),
        media_type="text/event-stream"
    )
```

### 2. Database Operations

```python
from app.utils.db_error_handler import with_database_error_handling, safe_database_transaction

@with_database_error_handling("user_creation")
async def create_user(db: AsyncSession, user_data: dict):
    async with safe_database_transaction(db) as session:
        user = User(**user_data)
        session.add(user)
        await session.flush()
        await session.refresh(user)
        return user
```

### 3. External API Calls

```python
from app.utils.api_error_handler import handle_openai_error, with_circuit_breaker

@handle_openai_error
@with_circuit_breaker("openai")
async def call_openai_api(prompt: str):
    # OpenAI API call implementation
    pass
```

### 4. File Operations

```python
from app.utils.error_handler import FileOperationError

def process_file(file_path: str):
    try:
        with open(file_path, 'r') as f:
            return f.read()
    except FileNotFoundError:
        raise FileOperationError(
            message="File not found",
            file_path=file_path,
            operation="read"
        )
    except PermissionError:
        raise FileOperationError(
            message="Permission denied",
            file_path=file_path,
            operation="read"
        )
```

### 5. Input Validation

```python
from app.utils.validation_error_handler import validate_request_data, validate_file_upload

@validate_request_data(UserCreateRequest)
@validate_file_upload(allowed_types=["image/jpeg", "image/png"], max_size_mb=5)
async def create_user_with_avatar(user_data: UserCreateRequest, file: UploadFile):
    # Implementation
    pass
```

## Error Codes Reference

### Validation Errors (1000-1999)
- `ERR_1001`: Invalid input
- `ERR_1002`: Missing required field
- `ERR_1003`: Invalid format
- `ERR_1004`: Invalid file type
- `ERR_1005`: File too large

### Authentication/Authorization (2000-2999)
- `ERR_2001`: Invalid token
- `ERR_2002`: Token expired
- `ERR_2003`: Insufficient permissions
- `ERR_2004`: Account locked

### External API Errors (3000-3999)
- `ERR_3001`: OpenAI API error
- `ERR_3002`: Perplexity API error
- `ERR_3003`: S3 API error
- `ERR_3004`: FMP API error
- `ERR_3005`: External API timeout
- `ERR_3006`: External API rate limit

### Database Errors (4000-4999)
- `ERR_4001`: Database connection error
- `ERR_4002`: Database query error
- `ERR_4003`: Database timeout
- `ERR_4004`: Database constraint violation

### File Operation Errors (5000-5999)
- `ERR_5001`: File not found
- `ERR_5002`: File upload failed
- `ERR_5003`: File processing error
- `ERR_5004`: S3 upload failed

### Business Logic Errors (6000-6999)
- `ERR_6001`: Resource not found
- `ERR_6002`: Duplicate resource
- `ERR_6003`: Operation not allowed
- `ERR_6004`: Quota exceeded

### System Errors (7000-7999)
- `ERR_7001`: Internal server error
- `ERR_7002`: Service unavailable
- `ERR_7003`: Configuration error

### Network Errors (8000-8999)
- `ERR_8001`: Connection timeout
- `ERR_8002`: Network unreachable
- `ERR_8003`: DNS resolution error

## Monitoring and Alerting

### Health Check Endpoints
- `GET /health` - Basic health check
- `GET /health/detailed` - Comprehensive health status
- `GET /health/errors` - Error metrics
- `GET /health/metrics/summary` - Key metrics summary

### Alert Conditions
- **Critical Errors**: Immediate email alerts
- **High Frequency**: 10+ errors of same type in 60 minutes
- **Error Spikes**: 20+ errors in 5 minutes
- **API Failures**: 5+ external API errors in 60 minutes

### Circuit Breaker Monitoring
- `GET /circuit-breaker/status` - All circuit breaker status
- `GET /circuit-breaker/status/{service}` - Specific service status
- `POST /circuit-breaker/reset/{service}` - Reset circuit breaker

## Best Practices

### 1. Error Creation
- Use specific error types for different categories
- Include relevant context in `additional_info`
- Provide user-friendly messages
- Preserve original exceptions for debugging

### 2. Error Handling
- Handle errors at the appropriate level
- Use circuit breakers for external services
- Implement proper retry mechanisms
- Log errors with appropriate severity

### 3. Monitoring
- Monitor error patterns and trends
- Set up appropriate alerting thresholds
- Review error metrics regularly
- Use health checks for system monitoring

### 4. Testing
- Test error scenarios thoroughly
- Validate error response formats
- Test circuit breaker behavior
- Verify monitoring functionality

## Migration Guide

### Updating Existing Code

1. **Replace generic exceptions**:
   ```python
   # Before
   raise Exception("Something went wrong")
   
   # After
   raise StandardizedError(
       code=ErrorCode.INTERNAL_SERVER_ERROR,
       message="Something went wrong",
       category=ErrorCategory.SYSTEM,
       severity=ErrorSeverity.HIGH
   )
   ```

2. **Add error handling decorators**:
   ```python
   # Before
   async def api_call():
       response = requests.get(url)
       return response.json()
   
   # After
   @handle_openai_error
   @with_circuit_breaker("external_api")
   async def api_call():
       response = requests.get(url)
       return response.json()
   ```

3. **Update database operations**:
   ```python
   # Before
   async def create_record(db, data):
       record = Model(**data)
       db.add(record)
       await db.commit()
       return record
   
   # After
   @with_database_error_handling("create_record")
   async def create_record(db, data):
       async with safe_database_transaction(db) as session:
           record = Model(**data)
           session.add(record)
           await session.flush()
           return record
   ```

## Troubleshooting

### Common Issues

1. **Circuit breaker stuck open**: Check service health and reset if needed
2. **High error rates**: Review error patterns and fix root causes
3. **Missing error context**: Ensure proper error creation with context
4. **Alert fatigue**: Adjust alerting thresholds appropriately

### Debugging Tools

- Error monitoring dashboard
- Circuit breaker status endpoints
- Health check endpoints
- Comprehensive logging with error context
