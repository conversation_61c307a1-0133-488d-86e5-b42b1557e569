import asyncio
import json
import re
from typing import Any, Dict, Optional

import nltk
from fastapi.background import BackgroundTasks
from nltk.corpus import stopwords
from sentence_transformers import SentenceTransformer

from app.core.config import settings
from app.prompts.generic_prompts import (
    STREAM_SUMMARY_PROMPt,
    first_prompt,
    trade_prompt,
)
from app.services.chat_with_websocket_data import execute
from app.services.claude_fmp_service import fmp_execute
from app.services.generic_chat_service import (
    check_response_relevance,
    classify_question,
    process_sql_query,
    trade_question,
)
from app.services.web_search_services import web_scrape_search
from app.utils import cache
from app.utils.deep_search import (
    analyze_and_generate_sub_questions,
    generate_sub_question_titles,
    generate_synthesis_title,
    research_sub_questions_parallel,
    synthesize_findings,
)
from app.utils.general_utils import cache_qa, get_cached_qa, get_reusable_response
from app.utils.generic_utils import (
    build_metric_json_data,
    check_question_from_history,
    extract_json_content,
    metric_execute_query,
    prepare_dataset,
    summary_stream_handler,
    websearch_stream_handler,
)
from app.utils.logger import generic_logger as logger
from app.utils.rds_agent_utils import rds_sql_agent

# Download stopwords once
nltk.download("stopwords")

# Database configuration
DATABASE_SELECTOR = {
    "sql_query_db1": settings.DATABASE_1,
    "sql_query_db2": settings.DATABASE_1,
    "sql_query_db3": settings.DATABASE_1,
    "sql_query_db4": settings.DATABASE_1,
    "sql_query_db5": settings.DATABASE_5,
    "sql_query_db6": settings.DATABASE_1,
    "sql_query_db7": settings.DATABASE_1,
}


def clean_text_with_remove_stopwords(text):
    # Convert to lowercase
    text = text.lower()

    # Remove special characters (keep only words and spaces)
    text = re.sub(r"[^a-z\s]", "", text)

    # Remove stopwords
    stop_words = set(stopwords.words("english"))
    words = text.split()
    filtered_words = [word for word in words if word not in stop_words]

    # Join the cleaned words back into a string
    return " ".join(filtered_words)


async def generic_chat_service(
    chat_id: str = "",
    account_id: int = "",
    trading_token: str = "",
    generic_chat_history: list = [],
    is_websearch_enabled: bool = False,
    question: Optional[str] = None,
    reply: Optional[str] = None,
    track: str = "",
    selected_category: Optional[str] = None,
    background_tasks: BackgroundTasks = None,
):
    """
    Non-streaming version of the chat service that returns the final result directly
    """
    try:
        history_key = f"chat_history_{chat_id}"
        chat_context_key = f"chat_context_{chat_id}"
        chat_context = cache.get(chat_context_key) or {}
        print(chat_context)
        print("coming to here -===================>")

        print(10.1)
        reusable_response = get_reusable_response(prompt=question)
        if reusable_response is not None:
            result = {
                "answer": reusable_response,
                "question": question,
                "type": "final",
            }
            return result

        cached_answer = get_cached_qa(question)
        if cached_answer is not None:
            result = {"answer": cached_answer, "question": question, "type": "final"}
            return result

        user_question = (
            f"Reply to: {reply}\n\nQuestion: {question}" if reply else question
        )
        if is_websearch_enabled:
            user_question += " Please search the internet for the answer."
        logger.info(f"User question: {user_question}")

        previous_question_check = check_question_from_history(
            user_question, generic_chat_history[-3:]
        )

        try:
            test_question = json.loads(previous_question_check)
        except Exception as e:
            test_question = {
                "response": "Null",
                "is_new_question": previous_question_check,
            }

        if test_question.get("response") != "Null":
            result = {
                "answer": test_question["response"],
                "question": question,
                "type": "final",
            }
            return result

        # If is_new_question exists, use it as user_question
        if test_question.get("is_new_question") != "Null":
            user_question = test_question["is_new_question"]
        else:
            user_question = question

        # Fetch account details
        try:
            query = f"SELECT token, customer FROM {settings.DATABASE_5.split('/')[-1]}.accounts WHERE id = :account_id"
            params = {"account_id": account_id}

            result_data = build_metric_json_data(
                metric_execute_query(query, settings.DATABASE_5, params)
            )
            account_id = result_data[0]["token"]
            customer_id = str(result_data[0]["customer"])
        except Exception as e:
            logger.error(f"Error fetching account data: {e}")
            customer_id = account_id = ""

        decoded_customer_id = track.split("_")[0]
        logger.info(
            f"Customer ID ====>: {customer_id}, Decoded ====>: {decoded_customer_id}, Account ID ====>: {account_id}"
        )

        if selected_category:
            # Collect all websearch data instead of streaming
            answer = ""
            sources = None
            try:
                async for chunk in websearch_stream_handler(user_question):
                    if chunk["type"] == "answer":
                        answer += chunk["data"]
                    else:
                        sources = chunk.get("data", {}).get("sources")
                        break
            except StopIteration:
                pass

            result = {
                "answer": answer,
                "question": question,
                "source": sources,
                "type": "final",
            }
            cache_qa(question, answer, ex=60 * 60 * 2)
            return result

        system_prompt = (
            f"- Note: Ensure using the user's ID in queries where possible. Unique ID: {account_id}"
            + first_prompt
        )

        logger.info("Calling classify_question function")
        first_result = await classify_question(user_question, system_prompt)
        logger.info("classify_question function executed successfully")

        cleaned_result = (
            first_result.replace("`", "").replace("json", "").replace("\n", "")
        )
        json_content = extract_json_content(cleaned_result)
        logger.info(f"json_content: {json_content}")

        if background_tasks:
            background_tasks.add_task(prepare_dataset, user_question, json_content)

        result = {}

        if json_content is None:
            result = {"answer": cleaned_result, "question": question, "type": "final"}
            cache_qa(question, cleaned_result, ex=60 * 60 * 24 * 30)
            return result

        # Step 2: Handle specific JSON content cases
        if json_content and "answer" in json_content:
            result = {
                "answer": json_content["answer"],
                "question": question,
                "type": "final",
            }
            cache_qa(question, json_content["answer"], ex=60 * 60 * 24 * 30)
            return result

        if json_content:
            sql_query_db5 = json_content.get("sql_query_db5")
            if sql_query_db5 and sql_query_db5.lower() == "websocket":
                answer = execute(trading_token, user_question)
                result = {"answer": answer, "question": question, "chat_id": chat_id}

            sql_query_db1 = json_content.get("sql_query_db1")
            if sql_query_db1 and sql_query_db1.lower() == "trade":
                answer = await trade_question(user_question, trade_prompt)
                cleaned_result = (
                    answer.replace("`", "").replace("json", "").replace("\n", "")
                )
                json_decoded_content = extract_json_content(cleaned_result)
                json_content["sql_query_db1"] = json_decoded_content.get(
                    "sql_query_db1"
                )
                json_content["chart_type"] = json_decoded_content.get("chart_type")
                if background_tasks:
                    background_tasks.add_task(
                        prepare_dataset, user_question, json_content
                    )

            sql_query_db8 = json_content.get("sql_query_db8")
            if sql_query_db8 and sql_query_db8.lower() == "fmpdata":
                answer = await fmp_execute(user_question, chat_id)
                if "chart_data" in answer.keys():
                    result = {
                        "answer": answer["summary"],
                        "question": question,
                        "chat_id": chat_id,
                        "chart_data": answer["chart_data"],
                    }
                else:
                    result = {
                        "answer": answer["summary"],
                        "question": question,
                        "chat_id": chat_id,
                    }

            sql_query_db9 = json_content.get("sql_query_db9")
            if sql_query_db9 and sql_query_db9.lower() == "websearch":
                answer = await web_scrape_search(user_question)
                result = {
                    "answer": answer["answer"],
                    "question": question,
                    "chat_id": chat_id,
                    "source": answer["sources"],
                }

            sql_query_db10 = json_content.get("sql_query_db10")
            if sql_query_db10 and sql_query_db10.lower() == "economicindicatordata":
                answer = await rds_sql_agent(
                    user_question, "economicindicatordata", track
                )
                result = {
                    "answer": answer["output"],
                    "question": question,
                    "chat_id": chat_id,
                }

            if result:
                answer = await check_response_relevance(
                    user_question, generic_chat_history, result["answer"]
                )
                if type(answer) == dict and "sources" in answer.keys():
                    result = {
                        "answer": answer["answer"],
                        "question": question,
                        "chat_id": chat_id,
                        "source": answer["sources"],
                    }
                else:
                    result["answer"] = result.get("answer")

                result.update({"type": "final"})
                cache_qa(question, result["answer"], ex=60 * 60 * 2)
                return result

        # Step 3: Process database queries
        result_db, event_ids, news_ids, metric_json = [], {}, [], None
        chart_type = json_content.pop("chart_type", None)

        if (
            json_content.get("sql_query_db1") != "null"
            and customer_id != decoded_customer_id
        ):
            logger.error("Unauthorized trade data access attempted")
            result = {
                "answer": "You don't have any trade data.",
                "question": question,
                "type": "final",
            }
            cache_qa(question, "You don't have any trade data.")
            return result

        for db_key, query in json_content.items():
            if query and query != "null":
                logger.info(f"Processing SQL query for {db_key}")

                db_result = await process_sql_query(
                    db_key, query, account_id, DATABASE_SELECTOR
                )
                logger.info(f"DB RESULT: {db_result}")
                result_db.append(db_result)

                if db_key == "sql_query_db2":
                    event_ids = {
                        row.get("date", "").split("T")[0]: [
                            row.get("ID") for row in db_result if row.get("ID")
                        ]
                        for row in db_result
                        if row.get("date")
                    }
                elif db_key == "sql_query_db3":
                    news_ids = [
                        row.get("news_id") for row in db_result if row.get("news_id")
                    ]
                elif db_key in {"sql_query_db1", "sql_query_db7"}:
                    metric_json = db_result

        # Search flow
        search_queries = {
            "sql_query_db2": event_ids,
            "sql_query_db3": news_ids,
            "sql_query_db4": result_db[-1] if result_db else [],
            "sql_query_db6": result_db[-1] if result_db else [],
            "sql_query_db7": result_db[-1] if result_db else [],
        }

        for db_key, query in json_content.items():
            if query != "null" and not search_queries.get(db_key, True):
                # Collect websearch data instead of streaming
                answer = ""
                sources = None
                try:
                    async for chunk in websearch_stream_handler(user_question):
                        if chunk["type"] == "answer":
                            answer += chunk["data"]
                        else:
                            sources = chunk.get("data", {}).get("sources")
                            break
                except StopIteration:
                    pass

                result = {
                    "answer": answer,
                    "question": question,
                    "source": sources,
                    "type": "final",
                }

                if json_content.get("sql_query_db1", "").lower() != "null":
                    cache_qa(question, answer, ex=60 * 60 * 24 * 30)
                return result

        # Step 4: Generate summary
        logger.info("Generating summary...")
        summary = ""
        end_stream_response = {}

        try:
            # Collect all summary data instead of streaming
            async for chunk in summary_stream_handler(
                user_question=user_question,
                json_result=result_db,
                history=generic_chat_history,
                summary_prompt=STREAM_SUMMARY_PROMPt,
            ):
                if chunk.get("type") == "answer":
                    summary += chunk.get("data", "")
                else:
                    end_stream_response = chunk.get("data", {})
        except StopIteration as e:
            logger.error(f"Stream ended")
            logger.info(f"Summary: {summary}")

        response = {
            "symbol": end_stream_response.get("symbol"),
            "event_ids": event_ids,
            "news_ids": news_ids,
            "metric_data": metric_json,
            "chart_type": chart_type,
            "start_date": end_stream_response.get("start_date"),
            "end_date": end_stream_response.get("end_date"),
        }

        logger.info("Database updated successfully.")
        logger.info(f"Response: {response}")

        response.update({"answer": summary, "type": "final", "question": question})

        if json_content.get("sql_query_db1", "").lower() == "null":
            cache_qa(question, summary)

        return response

    except Exception as e:
        logger.error(f"Error: {e}")
        # send_alert_mail(f"Error: {e} \n {traceback.format_exc()}")
        return {"error": "Internal server error", "type": "error", "chat_id": chat_id}


async def deep_search_handler(
    chat_id: str = "",
    question: Optional[str] = None,
    chat_history=None,
    parent: str = "",
) -> Dict[str, Any]:
    """
    Deep search functionality with dynamic titles and parallel processing.

    Args:
        chat_id: Chat ID
        question: User question
        chat_history: Chat history
        parent: Parent chat ID

    Returns:
        Dictionary containing the complete search results
    """
    history_key = f"chat_history_{chat_id}"

    data_to_save = dict()
    research_data = []
    try:
        # Step 1: Initial loading state
        resp = json.dumps(
            {"loading_message": "We're analyzing your question", "type": "loading"}
        )
        research_data.append(resp)

        # Step 2: Generate and research sub-questions
        logger.info("Starting deep search analysis")
        analysis, sub_questions = await analyze_and_generate_sub_questions(
            question, chat_history
        )
        # Replace ```html from analysis
        analysis = analysis.replace("```html", "").replace("```", "")

        # Yield the analysis immediately after getting it
        # print("Analysis: started ==========>")
        resp = json.dumps(
            {
                "loading_answer": analysis,
                "type": "final_answer",
            }
        )
        research_data.append(resp)

        # Step 3: Generate dynamic titles for sub-questions
        logger.info("Generating dynamic titles for sub-questions")
        sub_question_titles = await generate_sub_question_titles(
            question, sub_questions
        )

        # Step 4: Research all sub-questions in parallel
        logger.info("Starting parallel research of sub-questions")
        research_results = await research_sub_questions_parallel(sub_questions)

        # Step 5: Stream each sub-question with loading → answer → citations cycle
        data_to_save["research"] = []
        for i, res in enumerate(research_results):
            # Stream loading message for this sub-question
            title = (
                sub_question_titles[i]
                if i < len(sub_question_titles)
                else f"Analysis {i + 1}"
            )
            idx_key = str(i + 1)  # Start from 1 for better UX

            resp = json.dumps(
                {"loading_message": title, "type": "loading", "idx_key": idx_key}
            )
            research_data.append(resp)

            # Small delay to show loading state
            await asyncio.sleep(5.0)

            # Stream the answer for this sub-question
            resp = json.dumps(
                {
                    "loading_answer": res["answer"],
                    "type": "final_answer",
                    "idx_key": idx_key,
                }
            )
            research_data.append(resp)
            chat_to_dave = {"answer": res["answer"], "title": title, "index": i}
            # Stream citations if they exist
            if "citations" in res and res["citations"]:
                resp = json.dumps(
                    {
                        "citation_message": res["citations"],
                        "type": "links",
                        "idx_key": idx_key,
                    }
                )
                chat_to_dave["citations"] = res["citations"]
                research_data.append(resp)
            data_to_save["research"].append(chat_to_dave)

        # Step 6: Generate synthesis title and show loading
        logger.info("Generating synthesis title")
        synthesis_title = await generate_synthesis_title(question, sub_questions)

        # Calculate idx_key for synthesis (continues from sub-questions)
        synthesis_idx_key = str(len(research_results) + 1)

        resp = json.dumps(
            {
                "loading_message": synthesis_title,
                "type": "loading",
                "idx_key": synthesis_idx_key,
            }
        )
        research_data.append(resp)

        # Step 7: Generate and stream final synthesis
        logger.info("Generating final synthesis")
        synthesis_result = await synthesize_findings(question, research_results)

        resp = json.dumps(
            {
                "loading_answer": synthesis_result,
                "type": "final_answer",
                "idx_key": synthesis_idx_key,
            }
        )
        research_data.append(resp)

        # Add synthesis to research array as the final item
        synthesis_data = {
            "answer": synthesis_result,
            "title": synthesis_title,
            "index": len(research_results),  # Continue indexing from sub-questions
            "type": "synthesis",  # Mark this as synthesis for identification
        }
        data_to_save["research"].append(synthesis_data)
        # Step 8: Prepare complete HTML for database storage
        # Format analysis as HTML
        analysis_html = f'<div class="analysis">{analysis}</div>'

        # Format sub-questions as HTML
        sub_q_html = """
            <div class="sub-questions">
              <h2>Breaking down into sub-questions:</h2>
              <ol>
            """
        for q in sub_questions:
            sub_q_html += f"<li>{q}</li>\n"
        sub_q_html += """
              </ol>
            </div>
            """

        # Format research findings as HTML
        research_html = """
            <div class="research-findings">
              <h2>Research Findings:</h2>
            """
        for i, result_item in enumerate(research_results):
            research_html += f"""
                <div class="research-item">
                  <h3>Sub-question {i + 1}: {result_item['question']}</h3>
                  <div class="research-answer">
                    {result_item['answer']}
                  </div>
                </div>
                """
        research_html += "</div>"

        # Wrap the final synthesis in a div if it's not already wrapped
        if not synthesis_result.strip().startswith("<div"):
            final_html = f'<div class="comprehensive-answer">{synthesis_result}</div>'
        else:
            final_html = synthesis_result

        # Combine all HTML sections
        complete_html = f"""
            <div class="deep-search-result-container">
                {analysis_html}
                {sub_q_html}
                {research_html}
                {final_html}
            </div>
            """

        # Send final response with database info
        result = {
            "answer": final_html,
            "is_html": True,
            "database": complete_html,
            "reasearch_data": data_to_save,
        }

        resp = json.dumps(
            {
                "detail": result,
                "type": "final",
            }
        )
        research_data.append(resp)

        logger.info("Stream completed successfully")
        return {"answer": research_data}

    except Exception as e:
        logger.error(f"Error in analysis pipeline: {str(e)}", exc_info=True)
        research_data = []
        # Format error message as HTML
        error_html = f"""
            <div class="error-message">
                <h2>Error in Deep Search</h2>
                <p>I encountered an error while performing deep search:</p>
                <div class="error-details">
                    <p><strong>Error:</strong> {str(e)}</p>
                </div>
                <p>Please try again or rephrase your question.</p>
            </div>
            """

        result = {
            "answer": error_html,
            "chat_id": chat_id,
            "id": parent,
            "error": str(e),
            "is_html": True,
            "database": error_html,
            "reasearch_data": data_to_save,
        }

        resp = json.dumps(
            {
                "detail": result,
                "type": "final",
            }
        )
        research_data.append(resp)
        return {"answer": research_data}


# Global model instance to avoid reloading
_model = SentenceTransformer("all-MiniLM-L6-v2")


async def generate_and_cache_question_embeddings():
    """
    Function 1: Generate embeddings for all FAQ questions and store in cache

    Args:
        cache_key: Key to store embeddings in cache

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        qa_dict = cache.get("chat_context_default_faqs")
        dynamic_qa_dict = cache.get("chat_context_dynamic_faqs")
        deepsearch_qa_dict = cache.get("chat_context_default_deepsearch_faqs")
        deepsearch_dynamic_qa_dict = cache.get("chat_context_dynamic_deepsearch_faqs")

        if (
            not qa_dict
            and not dynamic_qa_dict
            and not deepsearch_dynamic_qa_dict
            and deepsearch_qa_dict
        ):
            logger.warning("No FAQ data found in cache")

        # Merge FAQ dictionaries
        qa_dict = qa_dict or {}
        dynamic_qa_dict = dynamic_qa_dict or {}
        updated_mapping_dict = {**qa_dict, **dynamic_qa_dict}
        updated_deepsearch_mapping_dict = {
            **deepsearch_qa_dict,
            **deepsearch_dynamic_qa_dict,
        }
        if not updated_mapping_dict:
            logger.warning("No FAQ questions found after merging")

        if not updated_deepsearch_mapping_dict:
            logger.warning("No FAQ questions found after merging")

        # Get model and prepare data
        questions = list(updated_mapping_dict.keys())
        deepsearch_questions = list(updated_deepsearch_mapping_dict.keys())
        answers = list(updated_mapping_dict.values())
        deepsearch_answers = list(updated_deepsearch_mapping_dict.values())
        print(questions)
        print(deepsearch_questions)

        updated_question_list = [
            clean_text_with_remove_stopwords(question) for question in questions
        ]
        print("both result same for all questions: ", questions == deepsearch_questions)
        updated_answer_list = [
            {"simple": answer_simple, "deepsearch": answer_deep}
            for answer_simple, answer_deep in zip(answers, deepsearch_answers)
        ]
        for i, question in enumerate(updated_question_list):
            print(
                f"question: {question}\nanswer_common: {updated_answer_list[i]['simple']}\nanswer_deepsearch: {updated_answer_list[i]['deepsearch']}"
            )

        logger.info(
            f"Generating embeddings for {len(updated_question_list)} FAQ questions"
        )

        # Generate embeddings for all questions
        question_embeddings = _model.encode(
            updated_question_list, convert_to_tensor=False
        )

        # Prepare data structure for caching
        embedding_data = {
            "questions": updated_question_list,
            "answers": updated_answer_list,
            "embeddings": question_embeddings.tolist(),  # Convert to list for JSON serialization
            "total_questions": len(updated_question_list),
        }

        # Store in cache
        cache.set_user_cache("chat_context_question_embeddings", embedding_data)

        logger.info(
            f"Successfully cached embeddings for {len(updated_question_list)} questions"
        )

    except Exception as e:
        logger.error(f"Error generating and caching question embeddings: {e}")


async def set_faqs_in_cache():
    """Set FAQs in cache by processing predefined questions"""
    try:
        common_question_list = [
            "Hi",
            "Hello",
            "Hey",
            "Good morning",
            "Good evening",
            "What's up?",
            "Bye",
            "Goodbye",
            "See you later",
            "Catch you later",
            "Talk to you soon",
            "I'm leaving now",
            "End the chat",
            "Thank you",
            "Thanks",
            "Appreciate it",
            "Many thanks",
            "Thanks a lot",
            "Much appreciated",
            "Thx",
            "How are you?",
            "How’s it going?",
            "Are you okay?",
            "How are things?",
            "Who are you?",
            "What can you do?",
            "Are you a human?",
            "Are you a bot?",
            "Tell me about yourself",
            "I need help",
            "Can you help me?",
            "Help me out",
            "Support",
        ]

        dynamic_question_list = [
            "give me upcoming economic events.",
            "what all stocks i need to invest today?",
            "Give me todays news which could impact eurusd?",
            "what all bitcoin i meed to invest for short term profit?",
            "What are today's major economic events?",
            "What all stocks should I invest in today?",
            "What are the best stocks to buy right now?",
            "Which stocks are trending today?",
            "Any financial news affecting crypto today?",
            "What are the best cryptocurrencies to buy for short-term profit?",
            "Which crypto coins are good for long-term investment?",
            "What is the best bitcoin strategy today?",
            "Which altcoins will pump soon?",
            "What are the top gainers in the stock market today?",
            "Show me today's stock market losers.",
            "What is the current price of gold?",
            "What is the live price of bitcoin?",
            "Give me real-time EUR/USD chart.",
            "What's the market sentiment for S&P 500?",
            "Is it a good time to buy Tesla stock?",
            "Should I invest in tech stocks now?",
            "What are the top performing mutual funds this month?",
            "What’s the best sector to invest in this week?",
            "Any buy/sell signals for Apple today?",
            "Show me technical indicators for BTC/USD.",
            "What is the RSI of ETH/USD?",
            "What is the support and resistance level for Nifty50?",
            "Suggest me low-risk investment options.",
            "How can I hedge my forex positions?",
            "What's the best strategy in a volatile market?",
            "Is scalping profitable in crypto?",
            "Give me forex trading ideas for today.",
            "What are the top forex pairs to watch?",
            "Show me major currency strength comparison.",
            "What are upcoming earnings reports?",
            "Which companies are reporting earnings today?",
            "Give me analyst ratings for Amazon.",
            "Which stocks have insider buying this week?",
            "What is the inflation rate in the US right now?",
            "Will Fed interest rate decision impact gold?",
            "How will the ECB decision affect EUR/USD?",
            "Is the market bullish or bearish today?",
            "Any news from the Federal Reserve today?",
            "What is the current fear and greed index?",
            "Should I short bitcoin now?",
            "What are safe haven assets today?",
            "Give me today's crude oil inventory data.",
            "What is the open interest in Nifty options?",
            "Which option strategy works for sideways markets?",
            "What is the implied volatility of Tesla options?",
            "How much margin do I need for trading forex?",
            "What leverage is best for crypto trading?",
            "What are some good swing trade setups?",
            "Suggest me intraday stock picks.",
            "What are the top short-selling opportunities today?",
            "How much should I allocate to crypto?",
            "What's the best asset allocation for high risk?",
            "Give me a portfolio suggestion for medium risk.",
            "How is USD/JPY performing today?",
            "What’s the impact of NFP on the dollar?",
            "Show me the correlation between gold and USD.",
            "Give me signals based on MACD for Ethereum.",
            "What is the 200-day moving average of BTC?",
            "Any breakout stocks today?",
            "Which stocks are near 52-week highs?",
            "Show me undervalued stocks right now.",
            "What is the dividend yield of Apple?",
            "Which stocks have ex-dividend date this week?",
            "What are the top ETFs to invest in?",
            "How is the Indian stock market performing today?",
            "What is the best strategy for news-based trading?",
            "Any arbitrage opportunities in crypto?",
        ]

        stored_common_question = {}

        stored_dynamic_question = {}

        stored_common_deepsearch_question = {}

        stored_dynamic_deepsearch_question = {}

        print(common_question_list)
        print(dynamic_question_list)

        async def process_multiple_chats(common_requests: list, dynamic_requests: list):
            """Process multiple chat requests concurrently"""

            async def process_single_chat(chat_data):
                return await generic_chat_service(**chat_data)

            async def process_single_deepsearch_chat(chat_data):
                return await deep_search_handler(**chat_data)

            # Process all common chats concurrently
            tasks = [process_single_chat(chat) for chat in common_requests]
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # Process all dynamic chats concurrently
            tasks_dynamic = [
                process_single_chat(dynamic_chat) for dynamic_chat in dynamic_requests
            ]
            dynamic_results = await asyncio.gather(
                *tasks_dynamic, return_exceptions=True
            )

            # Process all dynamic chats concurrently
            tasks_dynamic_deepsearch = [
                process_single_deepsearch_chat(dynamic_chat)
                for dynamic_chat in dynamic_requests
            ]
            dynamic_deepsearch_results = await asyncio.gather(
                *tasks_dynamic_deepsearch, return_exceptions=True
            )

            # Handle common question results
            common_processed_results = []
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    logger.error(f"Error processing chat {i}: {result}")
                    common_processed_results.append(
                        {
                            "question": common_requests[i].get("question", ""),
                            "answer": "<p>Could you please clarify what you’re asking about? Provide some more context.</p>",
                        }
                    )
                    continue

                res_question = result.get("question", "")
                try:
                    answer = result.get("answer", "")
                    common_processed_results.append(
                        {"question": res_question, "answer": answer}
                    )
                except Exception as e:
                    logger.error(f"Error extracting answer: {e}")
                    common_processed_results.append(
                        {
                            "question": res_question,
                            "answer": "<p>Could you please clarify what you’re asking about? Provide some more context.</p>",
                        }
                    )

            # Handle dynamic question results
            dynamic_processed_results = []
            for i, dynamic_result in enumerate(dynamic_results):
                if isinstance(dynamic_result, Exception):
                    logger.error(f"Error processing chat {i}: {dynamic_result}")
                    dynamic_processed_results.append(
                        {
                            "question": dynamic_requests[i].get("question", ""),
                            "answer": "<p>Could you please clarify what you’re asking about? Provide some more context.</p>",
                        }
                    )
                    continue

                dynamic_res_question = dynamic_result.get("question", "")
                try:
                    dynamic_answer = dynamic_result.get("answer", "")
                    dynamic_processed_results.append(
                        {"question": dynamic_res_question, "answer": dynamic_answer}
                    )
                except Exception as e:
                    logger.error(f"Error extracting answer: {e}")
                    dynamic_processed_results.append(
                        {
                            "question": dynamic_res_question,
                            "answer": "<p>Could you please clarify what you’re asking about? Provide some more context.</p>",
                        }
                    )

            common_processed_deepsearch_results = []
            for question in common_question_list:
                answer = "<p>Could you please clarify what you’re asking about? Provide some more context.</p>"
                try:
                    common_processed_deepsearch_results.append(
                        {"question": question, "answer": answer}
                    )
                except Exception as e:
                    logger.error(f"Error extracting answer: {e}")
                    common_processed_deepsearch_results.append(
                        {"question": question, "answer": answer}
                    )

            # Handle dynamic deepsearch question results
            dynamic_processed_deepsearch_results = []
            for i, dynamic_deepsearch_result in enumerate(dynamic_deepsearch_results):
                if isinstance(dynamic_deepsearch_result, Exception):
                    logger.error(
                        f"Error processing chat {i}: {dynamic_deepsearch_result}"
                    )
                    dynamic_processed_deepsearch_results.append(
                        {
                            "question": dynamic_question_list[i],
                            "answer": "<p>Could you please clarify what you’re asking about? Provide some more context.</p>",
                        }
                    )
                    continue

                dynamic_res_question = dynamic_question_list[i]
                try:
                    dynamic_answer = dynamic_deepsearch_result.get("answer", [])
                    dynamic_processed_deepsearch_results.append(
                        {"question": dynamic_res_question, "answer": dynamic_answer}
                    )
                except Exception as e:
                    logger.error(f"Error extracting answer: {e}")
                    dynamic_processed_deepsearch_results.append(
                        {
                            "question": dynamic_res_question,
                            "answer": "<p>Could you please clarify what you’re asking about? Provide some more context.</p>",
                        }
                    )

            return (
                common_processed_results,
                dynamic_processed_results,
                common_processed_deepsearch_results,
                dynamic_processed_deepsearch_results,
            )

        question_updated_list = [
            {"question": common_question} for common_question in common_question_list
        ]
        dynamic_updated_list = [
            {"question": dynamic_question} for dynamic_question in dynamic_question_list
        ]

        (
            common_question_res,
            dynamic_question_res,
            common_deepsearch_res,
            dynamic_deepsearch_res,
        ) = await process_multiple_chats(question_updated_list, dynamic_updated_list)

        for common_res in common_question_res:
            stored_common_question[common_res["question"]] = common_res["answer"]

        for common_deep_res in common_deepsearch_res:
            stored_common_deepsearch_question[
                common_deep_res["question"]
            ] = common_deep_res["answer"]

        for dynamic_res in dynamic_question_res:
            stored_dynamic_question[dynamic_res["question"]] = dynamic_res["answer"]

        for dynamic_deep_res in dynamic_deepsearch_res:
            stored_dynamic_deepsearch_question[
                dynamic_deep_res["question"]
            ] = dynamic_deep_res["answer"]

        print(len(stored_common_question.keys()))
        print(len(stored_dynamic_question.keys()))
        print(len(stored_dynamic_deepsearch_question.keys()))
        print(len(stored_common_deepsearch_question.keys()))
        cache.delete("chat_context_default_faqs")
        cache.delete("chat_context_dynamic_faqs")
        cache.delete("chat_context_default_deepsearch_faqs")
        cache.delete("chat_context_dynamic_deepsearch_faqs")
        cache.set_user_cache("chat_context_default_faqs", stored_common_question)
        cache.set_user_cache("chat_context_dynamic_faqs", stored_dynamic_question)
        cache.set_user_cache(
            "chat_context_default_deepsearch_faqs", stored_common_deepsearch_question
        )
        cache.set_user_cache(
            "chat_context_dynamic_deepsearch_faqs", stored_dynamic_deepsearch_question
        )
        logger.info(
            f"dynamic and common chat faqs question set successfully in redis cache"
        )
        await generate_and_cache_question_embeddings()
    except Exception as e:
        logger.error(f"Error in storing faqs based question on cache: {e}")


# Run once when module is imported (if you need immediate execution)
def run_faq_setup():
    """Run FAQ setup synchronously"""
    asyncio.run(set_faqs_in_cache())


if __name__ == "__main__":
    run_faq_setup()
else:
    run_faq_setup()
