#!/bin/bash

# Change ownership of Nginx log directory
sudo chown -R www-data:www-data /var/log/nginx

# Change permissions of chatbot service directory
sudo chmod 775 -R /home/<USER>
sudo chmod 775 -R /home/<USER>/chatbot-service

sudo chmod 775 -R /home/<USER>/chatbot-service/venv
sudo chown -R ubuntu:www-data /home/<USER>/chatbot-service
# sudo chmod -R 755 /home/<USER>/chatbot-service
# sudo chmod -R 775 /home/<USER>/chatbot-service/venv  # Adjust as necessary

# Add the 'ubuntu' user to the 'www-data' group
sudo usermod -a -G www-data ubuntu



# Retrieve the secret value once and store it in a variable.
#secret_value=$(aws secretsmanager get-secret-value --secret-id staging-aichat-service --query SecretString --output text)



echo "Setting APP_ENV tag"
APP_ENV=$(aws ec2 describe-tags --filters "Name=key,Values=APP_ENV" --query "Tags[0].Value" --output text)

echo "Setting up environment variables"

if [ "$APP_ENV" == "staging" ]; then
  echo "App environment is staging. Fetching secrets for staging..."
  aws secretsmanager get-secret-value --secret-id staging-aichat-service --query SecretString --output text > /home/<USER>/chatbot-service/.env
else
  echo "App environment is not staging. Fetching secrets for master..."
  aws secretsmanager get-secret-value --secret-id microservice-vuetra-ai-env --query SecretString --output text > /home/<USER>/chatbot-service/.env
fi

echo "Setting up variable"
if [ "$APP_ENV" == "staging" ]; then
  echo "App environment is staging. Fetching secrets for staging..."
  secret_value=$(aws secretsmanager get-secret-value --secret-id staging-aichat-service --query SecretString --output text)
else
  echo "App environment is not staging. Fetching secrets for master..."
  secret_value=$(aws secretsmanager get-secret-value --secret-id microservice-vuetra-ai-env --query SecretString --output text)
fi

# Extract the WORKERS value
WORKERS=$(echo "$secret_value" | grep 'WORKERS' | cut -d '=' -f 2)
echo "WORKERS: $WORKERS"

# Extract the SERVER_NAME value
SERVER_NAME=$(echo "$secret_value" | grep 'SERVER_NAME' | cut -d '=' -f 2 | tr -d '\r')

echo "SERVER_NAME: $SERVER_NAME"
  
echo "Copying public key"
if [ "$APP_ENV" == "staging" ]; then
  echo "App environment is staging. Fetching secrets for staging..."
  aws secretsmanager get-secret-value --secret-id microservice-customer-staging-public-key --query SecretString --output text > /home/<USER>/chatbot-service/app/utils/public.key
else
  echo "App environment is not staging. Fetching secrets for master..."
  aws secretsmanager get-secret-value --secret-id microservice-vuetra-customer-public-key --query SecretString --output text > /home/<USER>/chatbot-service/app/utils/public.key
fi


# Create a systemd service file for the chat_bot service
sudo tee /etc/systemd/system/chat_bot.service > /dev/null <<EOL
[Unit]
Description=Gunicorn instance to serve chat_bot
After=network.target

[Service]
User=ubuntu
Group=www-data
WorkingDirectory=/home/<USER>/chatbot-service
Environment="PATH=/home/<USER>/chatbot-service/venv/bin"
ExecStart=/home/<USER>/chatbot-service/venv/bin/gunicorn --workers 4 --threads 4 --timeout 300 --worker-class uvicorn.workers.UvicornWorker --bind 0.0.0.0:8000 app.main:app

[Install]
WantedBy=multi-user.target

EOL

# Start and enable the chat_bot service
sudo systemctl start chat_bot
sudo systemctl enable chat_bot
sudo chown www-data:www-data /home/<USER>/chatbot-service/chat_bot.sock
sudo chmod 660 /home/<USER>/chatbot-service/chat_bot.sock

# Create an Nginx server block for the chat_bot service
sudo tee /etc/nginx/sites-available/chat_bot > /dev/null <<EOL
# server {
#     listen 80;
#     listen [::]:80;
#     server_name $SERVER_NAME;
#     client_max_body_size 5m;

#     location / {
#         include proxy_params;
#         proxy_pass http://127.0.0.1:8000;
#         proxy_read_timeout 300s;
#         proxy_send_timeout 300s;
#         proxy_connect_timeout 300s;
#     }
# }
# server {
#     listen 80;
#     listen [::]:80;
#     server_name $SERVER_NAME;
#     client_max_body_size 5m;

#     location / {
#         include proxy_params;
#         proxy_pass http://127.0.0.1:8000;
#         proxy_read_timeout 300s;
#         proxy_send_timeout 300s;
#         proxy_connect_timeout 300s;

#         # CORS configuration for workspace.vuetra.com only
#         if ($http_origin = 'https://workspace.vuetra.com') {
#             add_header 'Access-Control-Allow-Origin' 'https://workspace.vuetra.com' always;
#             add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS, PUT, PATCH, DELETE' always;
#             add_header 'Access-Control-Allow-Headers' 'Authorization, Content-Type' always;
#             add_header 'Access-Control-Allow-Credentials' 'true' always;
            
#             if ($request_method = 'OPTIONS') {
#                 return 204;
#             }
#         }
#     }
# }
server {
    listen 80;
    listen [::]:80;
    server_name $SERVER_NAME;
    client_max_body_size 5m;

    location / {
        include proxy_params;
        proxy_pass http://127.0.0.1:8000;
        proxy_http_version 1.1;
        proxy_connect_timeout 300s;
        proxy_read_timeout 3600;
        proxy_send_timeout 3600;
        proxy_buffering off;
        proxy_cache off;
        proxy_set_header Connection '';
        chunked_transfer_encoding on;


        # Let FastAPI handle CORS
        # proxy_set_header Host $host;
        # proxy_set_header X-Real-IP $remote_addr;
        # proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        # proxy_set_header X-Forwarded-Proto $scheme;
    }
}




EOL

# Enable the chat_bot server block
sudo ln -s /etc/nginx/sites-available/chat_bot /etc/nginx/sites-enabled

# make backup of the default file
sudo cp /etc/nginx/sites-available/default /etc/nginx/sites-available/default.bak
sudo cp /etc/nginx/sites-enabled/default /etc/nginx/sites-enabled/default.bak


# can you remove the default file
sudo rm /etc/nginx/sites-enabled/default
sudo rm /etc/nginx/sites-available/default

# Test Nginx configuration
sudo nginx -t
sudo systemctl restart chat_bot
# Restart Nginx and allow incoming connections
sudo systemctl restart nginx

sudo ufw allow 'Nginx Full'