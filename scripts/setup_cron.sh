# setup cron job schedual everyday 11:00PM file script.py 

sudo apt-get install cron

# # Create a cron job
# cat <<EOT | crontab -
# 0 23 * * * /home/<USER>/chatbot-service/venv/bin/python /home/<USER>/chatbot-service/investing_calander.py
# EOT

# # # Add the cron job to the crontab if it doesn't already exist
# crontab -l | grep -q '/home/<USER>/chatbot-service/venv/bin/python /home/<USER>/chatbot-service/investing_calander.py' || echo "0 23 * * * /home/<USER>/chatbot-service/venv/bin/python /home/<USER>/chatbot-service/investing_calander.py" | crontab -

# # Add the cron job to the crontab if it doesn't already exist new_script_for_events.py
# crontab -l | grep -q '/home/<USER>/chatbot-service/venv/bin/python /home/<USER>/chatbot-service/new_script_for_events.py' || echo "0 23 * * * /home/<USER>/chatbot-service/venv/bin/python /home/<USER>/chatbot-service/new_script_for_events.py" | crontab -

# Create or Update the cron job
# cat <<EOT | crontab -
# 0 23 * * * /home/<USER>/chatbot-service/venv/bin/python /home/<USER>/chatbot-service/final_scraper.py
# EOT

# # Add the cron job to the crontab if it doesn't already exist
# crontab -l | grep -q '/home/<USER>/chatbot-service/venv/bin/python /home/<USER>/chatbot-service/final_scraper.py' || echo "0 23 * * * /home/<USER>/chatbot-service/venv/bin/python /home/<USER>/chatbot-service/final_scraper.py" | crontab -