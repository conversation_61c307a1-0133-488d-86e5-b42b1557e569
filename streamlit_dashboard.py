#!/usr/bin/env python3
"""
🤖 AI Cost Analysis Dashboard - Streamlit Version
Real-time monitoring of AI model usage and costs
"""

import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import time
from datetime import datetime, timedelta
import sys
import os
from typing import Dict, List, Any
import json

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.cost_dashboard_service import CostDashboardService

# Page configuration
st.set_page_config(
    page_title="🤖 AI Cost Analysis Dashboard",
    page_icon="🤖",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for better styling
st.markdown("""
<style>
    .main-header {
        font-size: 3rem;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
        background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        font-weight: bold;
    }

    .metric-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 1rem;
        border-radius: 10px;
        color: white;
        text-align: center;
        margin: 0.5rem 0;
    }

    .cost-highlight {
        font-size: 2rem;
        font-weight: bold;
        color: #e74c3c;
    }

    .success-metric {
        font-size: 1.5rem;
        color: #27ae60;
        font-weight: bold;
    }

    .info-metric {
        font-size: 1.2rem;
        color: #3498db;
        font-weight: bold;
    }

    .sidebar .sidebar-content {
        background: linear-gradient(180deg, #667eea 0%, #764ba2 100%);
    }
</style>
""", unsafe_allow_html=True)

# Initialize dashboard service
@st.cache_resource
def get_dashboard_service():
    return CostDashboardService()

dashboard_service = get_dashboard_service()

# Sidebar configuration
st.sidebar.title("⚙️ Dashboard Settings")
st.sidebar.markdown("---")

# Time range selector
time_range = st.sidebar.selectbox(
    "📅 Time Range",
    options=[1, 6, 12, 24, 48, 168],
    index=3,  # Default to 24 hours
    format_func=lambda x: f"Last {x} hour{'s' if x != 1 else ''}"
)

# Auto-refresh settings
auto_refresh = st.sidebar.checkbox("🔄 Auto Refresh", value=True)
refresh_interval = st.sidebar.slider("Refresh Interval (seconds)", 5, 60, 10)

# Filters
st.sidebar.markdown("### 🔍 Filters")
show_zero_cost = st.sidebar.checkbox("Show Zero Cost Requests", value=False)
min_cost_filter = st.sidebar.number_input("Minimum Cost ($)", min_value=0.0, value=0.0, step=0.0001, format="%.4f")

st.sidebar.markdown("---")
st.sidebar.markdown("### 📊 Dashboard Info")
st.sidebar.info("This dashboard updates in real-time as you make requests to /generic-v3")

# Main dashboard
def main():
    # Header
    st.markdown('<h1 class="main-header">🤖 AI Cost Analysis Dashboard</h1>', unsafe_allow_html=True)
    st.markdown("### Real-time monitoring of AI model usage and costs")

    # Auto-refresh logic
    if auto_refresh:
        placeholder = st.empty()
        with placeholder.container():
            render_dashboard()
        time.sleep(refresh_interval)
        st.rerun()
    else:
        render_dashboard()

def render_dashboard():
    try:
        # Load data
        with st.spinner("📊 Loading cost analysis data..."):
            dashboard_data = dashboard_service.get_dashboard_data_sync(hours_back=time_range)
            stats = dashboard_data['stats']

        # Apply filters
        if not show_zero_cost:
            stats['recent_requests'] = [r for r in stats['recent_requests'] if r['total_cost'] > 0]

        if min_cost_filter > 0:
            stats['recent_requests'] = [r for r in stats['recent_requests'] if r['total_cost'] >= min_cost_filter]

        # Display metrics
        render_metrics(stats)

        # Display charts
        render_charts(stats)

        # Display tables
        render_tables(stats)

        # Display last updated time
        st.markdown("---")
        col1, col2, col3 = st.columns([1, 2, 1])
        with col2:
            st.info(f"📅 Last Updated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} | 📊 Records Found: {dashboard_data['total_records_found']}")

    except Exception as e:
        st.error(f"❌ Error loading dashboard data: {str(e)}")
        st.info("💡 Make sure you have made some requests to /generic-v3 to generate cost data")

def render_metrics(stats):
    """Render key metrics cards"""
    st.markdown("## 📈 Key Metrics")

    col1, col2, col3, col4, col5, col6 = st.columns(6)

    with col1:
        st.metric(
            label="💰 Total Cost",
            value=f"${stats['total_cost']:.4f}",
            delta=None
        )

    with col2:
        st.metric(
            label="📊 Total Requests",
            value=f"{stats['total_requests']:,}",
            delta=None
        )

    with col3:
        st.metric(
            label="🎯 Total Tokens",
            value=f"{stats['total_tokens']:,}",
            delta=None
        )

    with col4:
        st.metric(
            label="💵 Avg Cost/Request",
            value=f"${stats['avg_cost_per_request']:.4f}",
            delta=None
        )

    with col5:
        st.metric(
            label="📝 Avg Tokens/Request",
            value=f"{int(stats['avg_tokens_per_request']):,}",
            delta=None
        )

    with col6:
        st.metric(
            label="⏱️ Avg Duration",
            value=f"{int(stats['avg_duration_ms'])}ms",
            delta=None
        )

def render_charts(stats):
    """Render interactive charts"""
    st.markdown("## 📊 Analytics Charts")

    # Row 1: Model costs and Flow distribution
    col1, col2 = st.columns(2)

    with col1:
        st.markdown("### 💰 Cost by Model")
        if stats['top_models']:
            model_df = pd.DataFrame(stats['top_models'])
            fig_model = px.pie(
                model_df,
                values='cost',
                names='model',
                title="Cost Distribution by AI Model",
                color_discrete_sequence=px.colors.qualitative.Set3
            )
            fig_model.update_traces(textposition='inside', textinfo='percent+label')
            fig_model.update_layout(height=400)
            st.plotly_chart(fig_model, use_container_width=True)
        else:
            st.info("No model data available")

    with col2:
        st.markdown("### 🔄 Requests by Flow Type")
        if stats['top_flows']:
            flow_df = pd.DataFrame(stats['top_flows'])
            fig_flow = px.bar(
                flow_df,
                x='flow',
                y='requests',
                title="Request Distribution by Flow Type",
                color='cost',
                color_continuous_scale='Viridis'
            )
            fig_flow.update_layout(height=400)
            st.plotly_chart(fig_flow, use_container_width=True)
        else:
            st.info("No flow data available")

    # Row 2: Hourly trends
    st.markdown("### 📈 Hourly Cost and Request Trends")
    if stats['cost_by_hour']:
        hourly_df = pd.DataFrame(stats['cost_by_hour'])
        hourly_df['hour'] = pd.to_datetime(hourly_df['hour'])

        # Create subplot with secondary y-axis
        fig_hourly = make_subplots(specs=[[{"secondary_y": True}]])

        # Add cost line
        fig_hourly.add_trace(
            go.Scatter(
                x=hourly_df['hour'],
                y=hourly_df['cost'],
                mode='lines+markers',
                name='Cost ($)',
                line=dict(color='#e74c3c', width=3),
                marker=dict(size=8)
            ),
            secondary_y=False,
        )

        # Add requests bar
        fig_hourly.add_trace(
            go.Bar(
                x=hourly_df['hour'],
                y=hourly_df['requests'],
                name='Requests',
                opacity=0.7,
                marker_color='#3498db'
            ),
            secondary_y=True,
        )

        # Update layout
        fig_hourly.update_xaxes(title_text="Time")
        fig_hourly.update_yaxes(title_text="Cost ($)", secondary_y=False)
        fig_hourly.update_yaxes(title_text="Number of Requests", secondary_y=True)
        fig_hourly.update_layout(
            title="Hourly Cost and Request Trends",
            height=500,
            hovermode='x unified'
        )

        st.plotly_chart(fig_hourly, use_container_width=True)
    else:
        st.info("No hourly data available")

def render_tables(stats):
    """Render data tables"""
    st.markdown("## 📋 Detailed Data")

    # Tabs for different tables
    tab1, tab2, tab3 = st.tabs(["🔥 Recent Requests", "🤖 Model Analytics", "🔄 Flow Analytics"])

    with tab1:
        st.markdown("### 🔥 Recent Requests")
        if stats['recent_requests']:
            requests_df = pd.DataFrame(stats['recent_requests'])

            # Format the dataframe
            requests_df['timestamp'] = pd.to_datetime(requests_df['timestamp']).dt.strftime('%H:%M:%S')
            requests_df['total_cost'] = requests_df['total_cost'].apply(lambda x: f"${x:.4f}")
            requests_df['total_tokens'] = requests_df['total_tokens'].apply(lambda x: f"{x:,}")
            requests_df['duration_ms'] = requests_df['duration_ms'].apply(lambda x: f"{int(x)}ms")
            requests_df['models_used'] = requests_df['models_used'].apply(lambda x: ', '.join(x) if x else 'None')

            # Rename columns for display
            display_df = requests_df.rename(columns={
                'request_id': 'Request ID',
                'flow_type': 'Flow Type',
                'question': 'Question',
                'models_used': 'Models Used',
                'total_cost': 'Cost',
                'total_tokens': 'Tokens',
                'duration_ms': 'Duration',
                'timestamp': 'Time'
            })

            # Display with selection
            selected_indices = st.dataframe(
                display_df,
                use_container_width=True,
                hide_index=True,
                selection_mode="single-row",
                on_select="rerun"
            )

            # Show detailed view if a row is selected
            if selected_indices and len(selected_indices.selection.rows) > 0:
                selected_idx = selected_indices.selection.rows[0]
                selected_request = stats['recent_requests'][selected_idx]

                st.markdown("#### 🔍 Request Details")
                col1, col2 = st.columns(2)

                with col1:
                    st.json({
                        "Request ID": selected_request['request_id'],
                        "Flow Type": selected_request['flow_type'],
                        "Total Cost": f"${selected_request['total_cost']:.4f}",
                        "Total Tokens": f"{selected_request['total_tokens']:,}",
                        "Duration": f"{int(selected_request['duration_ms'])}ms"
                    })

                with col2:
                    st.markdown("**Question:**")
                    st.text_area("", selected_request['question'], height=100, disabled=True)
                    st.markdown("**Models Used:**")
                    st.write(", ".join(selected_request['models_used']) if selected_request['models_used'] else "None")
        else:
            st.info("No recent requests found")

    with tab2:
        st.markdown("### 🤖 Model Analytics")
        if stats['top_models']:
            models_df = pd.DataFrame(stats['top_models'])

            # Add efficiency metrics
            models_df['cost_per_call'] = models_df['cost'] / models_df['calls']
            models_df['tokens_per_call'] = models_df['tokens'] / models_df['calls']

            # Format for display
            display_models = models_df.copy()
            display_models['cost'] = display_models['cost'].apply(lambda x: f"${x:.4f}")
            display_models['cost_per_call'] = display_models['cost_per_call'].apply(lambda x: f"${x:.4f}")
            display_models['tokens'] = display_models['tokens'].apply(lambda x: f"{x:,}")
            display_models['tokens_per_call'] = display_models['tokens_per_call'].apply(lambda x: f"{int(x):,}")

            display_models = display_models.rename(columns={
                'model': 'Model',
                'calls': 'API Calls',
                'cost': 'Total Cost',
                'tokens': 'Total Tokens',
                'cost_per_call': 'Cost/Call',
                'tokens_per_call': 'Tokens/Call'
            })

            st.dataframe(display_models, use_container_width=True, hide_index=True)
        else:
            st.info("No model data available")

    with tab3:
        st.markdown("### 🔄 Flow Analytics")
        if stats['top_flows']:
            flows_df = pd.DataFrame(stats['top_flows'])

            # Add efficiency metrics
            flows_df['cost_per_request'] = flows_df['cost'] / flows_df['requests']
            flows_df['avg_duration_sec'] = flows_df['avg_duration'] / 1000

            # Format for display
            display_flows = flows_df.copy()
            display_flows['cost'] = display_flows['cost'].apply(lambda x: f"${x:.4f}")
            display_flows['cost_per_request'] = display_flows['cost_per_request'].apply(lambda x: f"${x:.4f}")
            display_flows['avg_duration_sec'] = display_flows['avg_duration_sec'].apply(lambda x: f"{x:.2f}s")

            display_flows = display_flows.rename(columns={
                'flow': 'Flow Type',
                'requests': 'Total Requests',
                'cost': 'Total Cost',
                'cost_per_request': 'Cost/Request',
                'avg_duration_sec': 'Avg Duration'
            })

            st.dataframe(display_flows, use_container_width=True, hide_index=True)
        else:
            st.info("No flow data available")

# Run the main function
if __name__ == "__main__":
    main()